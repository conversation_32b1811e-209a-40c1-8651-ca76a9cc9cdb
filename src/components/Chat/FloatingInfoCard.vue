<template>
  <div v-if="visible" class="floating-info-card">
    <!-- 关闭按钮 (仅天气卡片使用) -->

    <!-- 天气信息卡片 -->
    <div v-if="cardType === 'weather'" class="card-content weather-card">
      <div class="card-header">
        <div class="card-icon weather-icon" @click="handlePlayWeatherContent">
          <TrumpetIcon width="32px" height="32px" color="var(--primary-color)" />
        </div>
        <div class="card-title-section">
          <h3 class="card-title">天气解读和预警</h3>
          <p class="card-subtitle">📍 AI智能分析</p>
        </div>
      </div>

      <div class="weather-content">
        <!-- 加载状态 -->
        <div v-if="loadingWeather" class="loading-state">
          <div class="loading-spinner"></div>
          <span class="loading-text">正在获取天气信息...</span>
        </div>

        <!-- AI提醒内容 -->
        <div v-else-if="weatherApiData && weatherApiData.ai_reminder" class="ai-reminder-content">
          <div
            class="ai-reminder-text markdown-body custom-markdown-body"
            v-html="renderMarkdown(weatherApiData.ai_reminder)"
          ></div>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="weatherError" class="error-state">
          <div class="error-icon">⚠️</div>
          <div class="error-text">{{ weatherError }}</div>
        </div>

        <!-- 默认状态 -->
        <div v-else class="default-state">
          <div class="default-text">暂无天气信息</div>
        </div>
      </div>
    </div>

    <!-- 记录您的情况卡片 -->
    <div v-else-if="cardType === 'record'" class="card-content record-card">
      <div class="record-row">
        <div class="card-icon record-icon">
          <svg
            width="32"
            height="32"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V8L14 2Z"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M14 2V8H20"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            /> 
            <path
              d="M16 17H8"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M10 9H9H8"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </div>
        <p class="record-text">跟老董聊聊，让我更懂你吧</p>
      </div>

      <!-- Section组件集成区域 -->
      <div v-if="showUserInfo" class="sections-container">
        <!-- 记忆时刻组件 -->
        <MemorySection
          v-if="userId && personDetail"
          ref="memorySectionRef"
          :person-detail="personDetail"
          :person-id="personDetail.person_id"
          :user-id="userId"
          @memory-add="handleMemoryAdd"
          @edit-memory="handleEditMemory"
          @delete-memory="handleDeleteMemory"
        />

        <!-- 个人信息组件 -->
        <InfoSection
          v-if="userId && personDetail"
          :person-detail="personDetail"
          :person-id="personDetail.person_id"
          :user-id="userId"
          @basic-info-mic-click="handleBasicInfoMicClick"
          @person-updated="handlePersonUpdated"
        />

        <!-- 提醒事项组件 -->
        <ReminderSection
          v-if="userId && personDetail"
          ref="reminderSectionRef"
          :person-detail="personDetail"
          :person-id="personDetail.person_id"
          :user-id="userId"
          :is-user-profile="isUserProfile"
          @add-reminder="handleReminderAdd"
          @edit-reminder="handleEditReminder"
          @delete-reminder="handleDeleteReminder"
        />

        <!-- 天气分析组件 -->
        <WeatherSection
          v-if="userId && personDetail"
          :person-detail="personDetail"
          :person-id="personDetail.person_id"
          :user-id="userId"
          @weather-mic-click="handleWeatherMicClick"
          @person-updated="handlePersonUpdated"
        />

        <!-- 推荐话题组件 -->
        <TopicSection
          v-if="userId && personDetail"
          :person-id="personDetail.person_id"
          :user-id="userId"
          @topic-click="handleTopicClick"
        />

        <!-- 生活方式组件（去过&想去、饮食偏好、期望、其他属性） -->
        <LifestyleSection
          v-if="userId && personDetail"
          :person-detail="personDetail"
          :person-id="personDetail.person_id"
          :user-id="userId"
          @attributes-updated="handleAttributesUpdated"
          @show-voice-chat="handleShowVoiceChat"
          @delete-lifestyle-item="handleDeleteLifestyleItem"
        />
      </div>
    </div>
  </div>

  <!-- 弹窗组件 - 在整个页面级别弹出 -->
  <!-- 事件添加弹窗 -->
  <AddEventDialog
    :show="showEventAddPopup"
    :user-id="userId || ''"
    :person-id="personDetail?.person_id || ''"
    @close="showEventAddPopup = false"
    @success="handleAddEventSuccess"
  />

  <!-- 事件编辑弹窗 -->
  <EditEventDialog
    :show="showEventEditPopup"
    :event-data="editingEvent"
    :user-id="userId || ''"
    @close="handleEventEditClose"
    @success="handleEventEditSuccess"
  />

  <!-- 提醒添加弹窗 -->
  <AddReminderDialog
    :show="showReminderAddPopup"
    :user-id="userId || ''"
    :person-id="personDetail?.person_id || ''"
    @close="showReminderAddPopup = false"
    @success="handleAddReminderSuccess"
  />

  <!-- 提醒编辑弹窗 -->
  <EditReminderDialog
    :show="showReminderEditPopup"
    :reminder-data="editingReminder"
    :user-id="userId || ''"
    @close="handleReminderEditClose"
    @success="handleReminderEditSuccess"
  />

  <!-- 事件直接编辑弹窗 -->
  <EventDirectEdit
    :show="showEventDirectEditPopup"
    :event-data="editingEventDirect"
    @close="handleEventDirectEditClose"
    @success="handleEventDirectEditSuccess"
  />

  <!-- 删除记忆确认弹窗 -->
  <DeleteConfirmDialog
    :visible="showDeleteMemoryDialog"
    content="确定要删除这个随手记吗？"
    :hint="memoryToDelete?.description_text || '随手记'"
    @confirm="confirmDeleteMemory"
    @cancel="closeDeleteMemoryDialog"
  />

  <!-- 删除提醒确认弹窗 -->
  <DeleteConfirmDialog
    :visible="showDeleteReminderDialog"
    content="确定要删除这个提醒事项吗？"
    :hint="reminderToDelete?.display_text || '提醒事项'"
    @confirm="confirmDeleteReminder"
    @cancel="closeDeleteReminderDialog"
  />

  <!-- 删除生活方式项目确认弹窗 -->
  <DeleteConfirmDialog
    :visible="showDeleteLifestyleDialog"
    :content="`确定要删除这个${lifestyleItemToDelete?.type}吗？`"
    :hint="lifestyleItemToDelete?.content || '生活方式项目'"
    @confirm="confirmDeleteLifestyleItem"
    @cancel="closeDeleteLifestyleDialog"
  />

  <!-- 编辑人员弹窗 -->
  <PersonEditDialog
    v-if="showEditDialog"
    :person="editPersonData"
    :user-id="userId || ''"
    :is-user-profile="isUserProfile"
    @close="handleCloseEditDialog"
    @success="handleEditSuccess"
  />
</template>

<script setup lang="ts">
import { ref, watch, onUnmounted, onMounted, computed, type Ref } from 'vue';
import {
  getComprehensiveWeather,
  type IComprehensiveWeatherResponse,
  getPersonWeather,
  deletePersonEvent,
  deleteReminder,
  type IPersonDetail,
  type IGetPersonWeatherResponse,
  type IReminder,
  type IRecommendedTopic,
  type IEvent,
} from '@/apis/memory';
import { getUserProfile, updatePerson, type IGetUserProfileResponse } from '@/apis/relation';
import { showFailToast, showSuccessToast } from 'vant';
import { weatherRefreshManager, type IWeatherRefreshEvent } from '@/utils/weatherRefresh';
import { useAudioQueue } from '@/pages/Chat/useAudioPlayer';
import MarkdownIt from 'markdown-it';
import 'github-markdown-css/github-markdown-light.css';
import '@/styles/markdown.scss';

// 导入Section组件
import MemorySection from '@/components/Sections/MemorySection.vue';
import ReminderSection from '@/components/Sections/ReminderSection.vue';
import InfoSection from '@/components/Sections/InfoSection.vue';
import WeatherSection from '@/components/Sections/WeatherSection.vue';
import LifestyleSection from '@/components/Sections/LifestyleSection.vue';
import TopicSection from '@/components/Sections/TopicSection.vue';

// 导入弹窗组件
import AddEventDialog from '@/components/Dialogs/AddEventDialog.vue';
import EditEventDialog from '@/components/Dialogs/EditEventDialog.vue';
import EventDirectEdit from '@/components/Dialogs/EventDirectEdit.vue';
import AddReminderDialog from '@/components/Dialogs/AddReminderDialog.vue';
import EditReminderDialog from '@/components/Dialogs/EditReminderDialog.vue';
import DeleteConfirmDialog from '@/components/Common/DeleteConfirmDialog.vue';
import PersonEditDialog from '@/components/Persons/PersonEditDialog.vue';
import TrumpetIcon from '@/assets/icons/TrumpetIcon.vue';
import DeleteIcon from '@/assets/icons/DeleteIcon.vue';

// 定义props
interface IProps {
  visible: boolean;
  cardType: 'weather' | 'record' | null;
  userId?: string;
}

const props = defineProps<IProps>();

// 定义emits
const emit = defineEmits<{
  close: [];
}>();

// 创建简化的markdown实例
const md = new MarkdownIt({
  html: false, // 不允许HTML标签
  breaks: true, // 支持换行
  linkify: true, // 自动识别链接
});

// markdown渲染函数
const renderMarkdown = (text: string): string => {
  return md.render(text);
};

// 音频播放相关
const { play, stop, isCurrentAudioPlaying } = useAudioQueue();

// 天气API相关状态
const loadingWeather = ref(false);
const weatherApiData: Ref<IComprehensiveWeatherResponse | null> = ref(null);
const weatherError = ref<string>('');

// PersonDetail相关状态
const loading = ref(true);
const loadingWeatherData = ref(true);
const personDetail = ref<IPersonDetail | null>(null);
const weatherData = ref<IGetPersonWeatherResponse | null>(null);

// 展开/收起状态
const showUserInfo = ref(true); // 默认展开状态

// 记忆时刻相关
const memorySectionRef = ref<InstanceType<typeof MemorySection> | null>(null);

// 提醒事项相关
const reminderSectionRef = ref<InstanceType<typeof ReminderSection> | null>(null);

// 删除记忆确认弹窗相关
const showDeleteMemoryDialog = ref(false);
const memoryToDelete = ref<IEvent | null>(null);

// 删除提醒确认弹窗相关
const showDeleteReminderDialog = ref(false);
const reminderToDelete = ref<IReminder | null>(null);

// 删除生活方式项目确认弹窗相关
const showDeleteLifestyleDialog = ref(false);
const lifestyleItemToDelete = ref<{
  type: string;
  content: string;
  category: 'travel' | 'food' | 'expectation' | 'other_attribute';
  index: number;
} | null>(null);

// 事件相关弹窗状态
const showEventAddPopup = ref(false);
const showEventEditPopup = ref(false);
const editingEvent = ref<IEvent | null>(null);
const showEventDirectEditPopup = ref(false);
const editingEventDirect = ref<IEvent | null>(null);

// 提醒相关弹窗状态
const showReminderAddPopup = ref(false);
const showReminderEditPopup = ref(false);
const editingReminder = ref<IReminder | null>(null);

// 编辑人员弹窗相关
const showEditDialog = ref(false);
const editPersonData = ref<IPersonDetail | null>(null);

// 计算属性：判断是否为用户档案
const isUserProfile = computed(() => {
  return true; // 对于FloatingInfoCard，总是显示用户自己的档案
});

// 定时器相关
let weatherUpdateTimer: NodeJS.Timeout | null = null;

// 天气数据刷新订阅
let weatherRefreshUnsubscribe: (() => void) | null = null;

// 处理天气数据刷新事件
const handleWeatherRefresh = (event: IWeatherRefreshEvent) => {
  if (event.type === 'comprehensive-weather' && event.userId === props.userId) {
    console.log('🔄 [FloatingInfoCard] 收到综合天气数据刷新事件，更新ai-reminder-content');
    // 更新天气数据
    weatherApiData.value = event.data as IComprehensiveWeatherResponse;
    weatherError.value = '';
  }
};

// 获取天气数据
const loadWeatherData = async () => {
  if (!props.userId) {
    weatherError.value = '缺少用户信息，无法获取天气数据';
    return;
  }

  try {
    loadingWeather.value = true;
    weatherError.value = '';

    console.log('🔄 [FloatingInfoCard.vue] 开始获取综合天气数据...', {
      userId: props.userId,
    });

    const response = await getComprehensiveWeather({
      user_id: props.userId,
    });

    console.log('📡 [FloatingInfoCard.vue] 综合天气数据响应:', response);

    if (response && response.result === 'success') {
      weatherApiData.value = response;
      console.log('✅ [FloatingInfoCard.vue] 综合天气数据加载成功');
    } else {
      weatherError.value = '天气数据获取失败';
      console.warn('⚠️ [FloatingInfoCard.vue] 综合天气数据格式异常:', response);
    }
  } catch (error) {
    console.error('❌ [FloatingInfoCard.vue] 获取综合天气数据失败:', error);
    weatherError.value = '网络请求失败，请稍后重试';
  } finally {
    loadingWeather.value = false;
  }
};

// 获取人员详情数据
const loadPersonDetail = async () => {
  try {
    loading.value = true;
    console.log('🔄 [FloatingInfoCard.vue] 开始获取人员详情...', {
      userId: props.userId,
      isUserProfile: isUserProfile.value,
    });

    if (!props.userId) {
      console.error('❌ [FloatingInfoCard.vue] 缺少必要参数');
      return;
    }

    // 对于FloatingInfoCard，总是获取用户档案
    const response = await getUserProfile({
      user_id: props.userId,
    });

    console.log('📡 [FloatingInfoCard.vue] 用户档案响应:', response);

    if (response && response.result === 'success' && response.person) {
      personDetail.value = response.person as IPersonDetail;
      console.log(
        '✅ [FloatingInfoCard.vue] 用户档案加载成功，person_id:',
        personDetail.value.person_id,
      );
    } else {
      console.warn('⚠️ [FloatingInfoCard.vue] 用户档案格式异常:', response);
    }
  } catch (error) {
    console.error('❌ [FloatingInfoCard.vue] 获取用户档案失败:', error);
  } finally {
    loading.value = false;
  }
};

// 获取天气数据
const loadWeatherDataForPerson = async () => {
  try {
    loadingWeatherData.value = true;

    if (!props.userId || !personDetail.value?.person_id) {
      console.error('❌ [FloatingInfoCard.vue] 缺少必要参数');
      weatherData.value = null;
      return;
    }

    console.log('🔄 [FloatingInfoCard.vue] 开始获取天气数据...', {
      userId: props.userId,
      personId: personDetail.value.person_id,
      isUserProfile: isUserProfile.value,
    });

    const response = await getPersonWeather({
      user_id: props.userId,
      person_id: personDetail.value.person_id,
    });

    console.log('📡 [FloatingInfoCard.vue] 天气数据响应:', response);
    weatherData.value = response;
  } catch (error) {
    console.error('❌ [FloatingInfoCard.vue] 获取天气数据失败:', error);
    weatherData.value = null;
  } finally {
    loadingWeatherData.value = false;
  }
};

// 记忆时刻相关处理函数
const handleMemoryAdd = () => {
  console.log('🔄 [FloatingInfoCard.vue] 添加记忆时刻');
  showEventAddPopup.value = true;
};

const handleEditMemory = (memory: IEvent) => {
  console.log('🔄 [FloatingInfoCard.vue] 编辑记忆时刻:', memory);
  editingEventDirect.value = memory;
  showEventDirectEditPopup.value = true;
};

const handleDeleteMemory = (memory: IEvent) => {
  console.log('🔄 [FloatingInfoCard.vue] 删除记忆时刻:', memory);
  memoryToDelete.value = memory;
  showDeleteMemoryDialog.value = true;
};

// 提醒事项相关处理函数
const handleReminderAdd = () => {
  console.log('🔄 [FloatingInfoCard.vue] 添加提醒事项');
  console.log('🔄 [FloatingInfoCard.vue] showReminderAddPopup 当前值:', showReminderAddPopup.value);
  console.log('🔄 [FloatingInfoCard.vue] userId:', props.userId);
  console.log('🔄 [FloatingInfoCard.vue] personDetail:', personDetail.value);
  showReminderAddPopup.value = true;
  console.log(
    '🔄 [FloatingInfoCard.vue] showReminderAddPopup 设置后值:',
    showReminderAddPopup.value,
  );
};

const handleEditReminder = (reminder: IReminder) => {
  console.log('🔄 [FloatingInfoCard.vue] 编辑提醒事项:', reminder);
  editingReminder.value = reminder;
  showReminderEditPopup.value = true;
};

const handleDeleteReminder = (reminder: IReminder) => {
  console.log('🔄 [FloatingInfoCard.vue] 删除提醒事项:', reminder);
  reminderToDelete.value = reminder;
  showDeleteReminderDialog.value = true;
};

// 个人信息相关处理函数
const handleBasicInfoMicClick = () => {
  console.log('🔄 [FloatingInfoCard.vue] 个人信息语音点击');
  // 这里可以添加具体的语音处理逻辑
};

const handlePersonUpdated = (updatedPersonDetail: IPersonDetail) => {
  console.log('🔄 [FloatingInfoCard.vue] 人员信息已更新:', updatedPersonDetail);
  personDetail.value = updatedPersonDetail;
};

// 天气分析相关处理函数
const handleWeatherMicClick = () => {
  console.log('🔄 [FloatingInfoCard.vue] 天气分析语音点击');
  // 这里可以添加具体的语音处理逻辑
};

// 推荐话题相关处理函数
const handleTopicClick = (topic: IRecommendedTopic) => {
  console.log('🔄 [FloatingInfoCard.vue] 话题点击:', topic);
  // 这里可以添加具体的话题处理逻辑
};

// 生活方式相关处理函数
const handleAttributesUpdated = (newAttributes: Record<string, string>) => {
  console.log('🔄 [FloatingInfoCard.vue] 属性已更新:', newAttributes);
  if (personDetail.value) {
    personDetail.value.key_attributes = newAttributes;
  }
};

const handleShowVoiceChat = () => {
  console.log('🔄 [FloatingInfoCard.vue] 显示语音聊天');
  // 这里可以添加具体的语音聊天逻辑
};

// 生活方式删除相关处理函数
const handleDeleteLifestyleItem = (deleteInfo: {
  type: string;
  content: string;
  category: 'travel' | 'food' | 'expectation' | 'other_attribute';
  index: number;
}) => {
  console.log('🔄 [FloatingInfoCard.vue] 删除生活方式项目:', deleteInfo);
  lifestyleItemToDelete.value = deleteInfo;
  showDeleteLifestyleDialog.value = true;
};

// 处理切换用户信息显示
const handleToggleUserInfo = () => {
  showUserInfo.value = !showUserInfo.value;
};

// 弹窗相关处理函数
const handleAddEventSuccess = () => {
  console.log('✅ [FloatingInfoCard] 事件添加成功');
  showEventAddPopup.value = false;
  // 延迟1100ms后刷新MemorySection数据，确保ES数据库已完成写入
  setTimeout(() => {
    console.log('🔄 [FloatingInfoCard] 延迟刷新MemorySection数据（添加事件成功）');
    if (memorySectionRef.value) {
      memorySectionRef.value.loadMemories();
    }
  }, 1100);
};

const handleEventEditClose = () => {
  showEventEditPopup.value = false;
  editingEvent.value = null;
};

const handleEventEditSuccess = () => {
  showEventEditPopup.value = false;
  editingEvent.value = null;
  // 延迟1100ms后刷新MemorySection数据，确保ES数据库已完成写入
  setTimeout(() => {
    console.log('🔄 [FloatingInfoCard] 延迟刷新MemorySection数据（编辑事件成功）');
    if (memorySectionRef.value) {
      memorySectionRef.value.loadMemories();
    }
  }, 1100);
};

const handleEventDirectEditClose = () => {
  showEventDirectEditPopup.value = false;
  editingEventDirect.value = null;
};

const handleEventDirectEditSuccess = () => {
  showEventDirectEditPopup.value = false;
  editingEventDirect.value = null;
  // 延迟1100ms后刷新MemorySection数据，确保ES数据库已完成写入
  setTimeout(() => {
    console.log('🔄 [FloatingInfoCard] 延迟刷新MemorySection数据（事件直接编辑成功）');
    if (memorySectionRef.value) {
      memorySectionRef.value.loadMemories();
    }
  }, 1100);
};

// 提醒弹窗相关处理函数
const handleAddReminderSuccess = () => {
  console.log('✅ [FloatingInfoCard] 提醒添加成功');
  showReminderAddPopup.value = false;
  // 延迟1100ms后刷新ReminderSection数据，确保ES数据库已完成写入
  setTimeout(() => {
    console.log('🔄 [FloatingInfoCard] 延迟刷新ReminderSection数据（添加提醒成功）');
    if (reminderSectionRef.value) {
      reminderSectionRef.value.loadReminders();
    }
  }, 1100);
};

const handleReminderEditClose = () => {
  showReminderEditPopup.value = false;
  editingReminder.value = null;
};

const handleReminderEditSuccess = () => {
  showReminderEditPopup.value = false;
  editingReminder.value = null;
  // 延迟1100ms后刷新ReminderSection数据，确保ES数据库已完成写入
  setTimeout(() => {
    console.log('🔄 [FloatingInfoCard] 延迟刷新ReminderSection数据（编辑提醒成功）');
    if (reminderSectionRef.value) {
      reminderSectionRef.value.loadReminders();
    }
  }, 1100);
};

const confirmDeleteMemory = async () => {
  if (!memoryToDelete.value) return;

  try {
    console.log('🔄 [FloatingInfoCard] 开始删除记忆:', memoryToDelete.value);

    const response = await deletePersonEvent({
      user_id: props.userId || '',
      event_id: memoryToDelete.value.event_id,
    });

    if (response && response.result === 'success') {
      showSuccessToast('记忆删除成功');
      closeDeleteMemoryDialog();
      // 延迟1100ms后刷新记忆数据，确保ES数据库已完成删除
      setTimeout(() => {
        console.log('🔄 [FloatingInfoCard] 延迟刷新MemorySection数据（删除记忆成功）');
        if (memorySectionRef.value) {
          memorySectionRef.value.loadMemories();
        }
      }, 1100);
    } else {
      showFailToast('删除记忆失败');
    }
  } catch (error) {
    console.error('❌ [FloatingInfoCard] 删除记忆失败:', error);
    showFailToast('删除记忆失败');
  }
};

const closeDeleteMemoryDialog = () => {
  showDeleteMemoryDialog.value = false;
  memoryToDelete.value = null;
};

const confirmDeleteReminder = async () => {
  if (!reminderToDelete.value) return;

  try {
    console.log('🔄 [FloatingInfoCard] 开始删除提醒:', reminderToDelete.value);

    const response = await deleteReminder({
      user_id: props.userId || '',
      reminder_id: reminderToDelete.value.reminder_id,
    });

    if (response && response.success) {
      showSuccessToast('提醒删除成功');
      closeDeleteReminderDialog();
      // 延迟1100ms后刷新提醒数据，确保ES数据库已完成删除
      setTimeout(() => {
        console.log('🔄 [FloatingInfoCard] 延迟刷新ReminderSection数据（删除提醒成功）');
        if (reminderSectionRef.value) {
          reminderSectionRef.value.loadReminders();
        }
      }, 1100);
    } else {
      showFailToast(`删除提醒失败：${response?.message || '未知错误'}`);
    }
  } catch (error) {
    console.error('❌ [FloatingInfoCard] 删除提醒失败:', error);
    showFailToast('删除提醒失败');
  }
};

const closeDeleteReminderDialog = () => {
  showDeleteReminderDialog.value = false;
  reminderToDelete.value = null;
};

const confirmDeleteLifestyleItem = async () => {
  if (!lifestyleItemToDelete.value || !personDetail.value) return;

  try {
    console.log('🔄 [FloatingInfoCard] 开始删除生活方式项目:', lifestyleItemToDelete.value);

    const attributes = { ...(personDetail.value.key_attributes as Record<string, string>) };
    const { category, index } = lifestyleItemToDelete.value;

    if (category === 'travel') {
      // 删除旅行记录
      const travelHistory = attributes['旅游历史'];
      if (travelHistory) {
        const travelList = travelHistory
          .split('|')
          .map((item) => item.trim())
          .filter((item) => item);
        travelList.splice(index, 1);

        if (travelList.length > 0) {
          attributes['旅游历史'] = travelList.join('|');
        } else {
          delete attributes['旅游历史'];
        }
      }
    } else if (category === 'food') {
      // 删除饮食偏好
      const foodPreferences = attributes['餐饮偏好'];
      if (foodPreferences) {
        const foodList = foodPreferences
          .split('|')
          .map((item) => item.trim())
          .filter((item) => item);
        foodList.splice(index, 1);

        if (foodList.length > 0) {
          attributes['餐饮偏好'] = foodList.join('|');
        } else {
          delete attributes['餐饮偏好'];
        }
      }
    } else if (category === 'expectation') {
      // 删除期望
      const expectationKeys = ['期望', '我的期望', '期待', '愿望', '目标'];
      const foundKey = expectationKeys.find((key) => attributes[key]);
      const targetKey = foundKey || '';
      const expectationValue = foundKey ? attributes[foundKey] : '';

      if (targetKey && expectationValue) {
        const expectationList = expectationValue
          .split('|')
          .map((item) => item.trim())
          .filter((item) => item);
        expectationList.splice(index, 1);

        if (expectationList.length > 0) {
          attributes[targetKey] = expectationList.join('|');
        } else {
          delete attributes[targetKey];
        }
      }
    } else if (category === 'other_attribute') {
      // 删除其他属性
      const knownKeys = ['当前城市', '旅游历史', '餐饮偏好', '期望', '我的期望', '期待', '愿望', '目标'];

      // 获取其他属性列表
      const otherAttributes: Array<{ key: string; value: string }> = [];
      Object.entries(attributes).forEach(([key, value]) => {
        if (!knownKeys.includes(key) && value && value.trim()) {
          otherAttributes.push({ key, value });
        }
      });

      if (index >= 0 && index < otherAttributes.length) {
        const keyToDelete = otherAttributes[index].key;
        delete attributes[keyToDelete];
      }
    }

    // 调用API更新人员属性
    await updatePersonAttributes(attributes);
    showSuccessToast('删除成功');
    closeDeleteLifestyleDialog();
  } catch (error) {
    console.error('❌ [FloatingInfoCard] 删除生活方式项目失败:', error);
    showFailToast('删除失败');
  }
};

const closeDeleteLifestyleDialog = () => {
  showDeleteLifestyleDialog.value = false;
  lifestyleItemToDelete.value = null;
};

// 更新人员属性的通用函数
const updatePersonAttributes = async (newAttributes: Record<string, string>) => {
  if (!personDetail.value) return;

  const aliases = personDetail.value.aliases || '';
  const submitAliases = aliases === '' ? '' : aliases;

  const response = await updatePerson(personDetail.value.person_id, {
    user_id: props.userId || '',
    canonical_name: personDetail.value.canonical_name,
    aliases: submitAliases,
    relationships: personDetail.value.relationships as string[],
    profile_summary: personDetail.value.profile_summary,
    key_attributes: newAttributes,
    is_user: personDetail.value.is_user,
    avatar: personDetail.value.avatar,
  });

  if (response && response.result === 'success') {
    // 更新本地数据
    personDetail.value.key_attributes = newAttributes;
    // 通知生活方式组件属性已更新
    handleAttributesUpdated(newAttributes);
  } else {
    throw new Error('更新失败');
  }
};

const handleCloseEditDialog = () => {
  showEditDialog.value = false;
  editPersonData.value = null;
};

const handleEditSuccess = () => {
  showEditDialog.value = false;
  editPersonData.value = null;
  // 重新加载人员详情
  void loadPersonDetail();
};

// 启动天气数据定时更新
const startWeatherTimer = () => {
  // 清除现有定时器
  clearWeatherTimer();

  // 立即加载一次数据
  void loadWeatherData();

  // 注释掉定时器，关闭定时更新功能
  // weatherUpdateTimer = setInterval(() => {
  //   void loadWeatherData();
  // }, 60000); // 60秒 = 60000毫秒

  console.log('[FloatingInfoCard.vue] 天气数据已加载，定时更新已关闭');
};

// 清除天气数据定时器
const clearWeatherTimer = () => {
  if (weatherUpdateTimer) {
    clearInterval(weatherUpdateTimer);
    weatherUpdateTimer = null;
    console.log('⏹️ [FloatingInfoCard.vue] 天气数据定时器已清除');
  }
};

// 监听visible和cardType变化，当天气卡片显示时启动定时器，当record卡片显示时加载数据
watch(
  () => [props.visible, props.cardType, props.userId],
  ([visible, cardType, userId]) => {
    if (visible && cardType === 'weather' && userId) {
      startWeatherTimer();
    } else {
      clearWeatherTimer();
    }

    // 当record卡片显示时，先加载人员详情，再加载天气数据
    if (visible && cardType === 'record' && userId) {
      void loadPersonDetail()
        .then(() => {
          // 人员详情加载完成后再加载天气数据
          void loadWeatherDataForPerson();
        })
        .catch((error) => {
          console.error('❌ [FloatingInfoCard.vue] 加载数据失败:', error);
        });
    }
  },
  { immediate: true }, // 在 watch 选项中，immediate: true 会让观察者在初始化时立即执行一次，无论被观察的属性是否发生变化。
);

// 组件挂载时订阅天气数据刷新事件和加载数据
onMounted(() => {
  weatherRefreshUnsubscribe = weatherRefreshManager.subscribe(handleWeatherRefresh);

  // 当record卡片显示时，先加载人员详情，再加载天气数据
  if (props.visible && props.cardType === 'record' && props.userId) {
    void loadPersonDetail()
      .then(() => {
        // 人员详情加载完成后再加载天气数据
        void loadWeatherDataForPerson();
      })
      .catch((error) => {
        console.error('❌ [FloatingInfoCard.vue] 初始化加载数据失败:', error);
      });
  }
});

// 处理关闭
const handleClose = () => {
  emit('close');
};

// Section组件相关处理函数将在后续添加

// 固定的音频ID，用于天气内容播放
const weatherAudioId = 'weather-content-audio';

// 处理朗读天气内容
const handlePlayWeatherContent = () => {
  // 获取要朗读的文本内容
  let textToRead = '';

  if (loadingWeather.value) {
    textToRead = '正在获取天气信息...';
  } else if (weatherApiData.value && weatherApiData.value.ai_reminder) {
    textToRead = weatherApiData.value.ai_reminder;
  } else if (weatherError.value) {
    textToRead = weatherError.value;
  } else {
    textToRead = '暂无天气信息';
  }

  // 检查当前是否正在播放天气内容
  if (isCurrentAudioPlaying(weatherAudioId)) {
    // 如果正在播放，则停止播放
    stop();
  } else {
    // 如果没有播放，则开始播放
    play({
      id: weatherAudioId,
      text: textToRead,
      type: 'manualPlay',
    });
  }
};

// 组件卸载时清除定时器
onUnmounted(() => {
  clearWeatherTimer();

  // 取消天气数据刷新订阅
  if (weatherRefreshUnsubscribe) {
    weatherRefreshUnsubscribe();
    weatherRefreshUnsubscribe = null;
  }
});
</script>

<style lang="scss" scoped>
.floating-info-card {
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 14px;
  margin: 16px 32px;
  position: relative;
  border: 2px solid var(--border-accent);
  animation: slideInDown 0.3s ease-out;
  z-index: 10;
  max-width: 640px;
}

.card-content {
  width: 100%;
}

.card-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
  gap: 16px;
}

.card-icon {
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  flex-shrink: 0;
  transition: all 0.3s ease;
  cursor: pointer;
}

.card-title-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.card-title {
  font-size: var(--font-size-2xl);
  font-weight: 600;
  color: var(--primary-color);
  margin: 0;
  line-height: 1.2;
}

.card-subtitle {
  font-size: var(--font-size-xl);
  font-weight: 400;
  color: var(--primary-color-timestamp);
  margin: 0;
  line-height: 1.3;
}

.weather-content {
  width: 100%;
}

// 加载状态样式
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 24px;
  color: var(--primary-color);
}

.loading-spinner {
  width: 28px;
  height: 28px;
  border: 2px solid var(--border-glass);
  border-top: 2px solid var(--accent-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 22px;
}

// AI提醒内容样式
.ai-reminder-content {
  max-height: 250px;
  padding: 20px;
  background: var(--primary-color-light);
  border-radius: 12px;
  border: 1px solid var(--border-glass);
  border-left: 3px solid var(--accent-color);
  overflow-y: auto;
}

.ai-reminder-text {
  font-size: calc(var(--font-size-lg) + 4px);
  line-height: 1.6;
  color: var(--person-detail-context);
  white-space: pre-wrap;
}

// 错误状态样式
.error-state {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 20px;
  background: rgba(255, 107, 107, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(255, 107, 107, 0.3);
}

.error-icon {
  font-size: var(--font-size-xl);
  flex-shrink: 0;
}

.error-text {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
}

// 默认状态样式
.default-state {
  padding: 20px;
  text-align: center;
  color: var(--text-secondary);
}

.default-text {
  font-size: var(--font-size-base);
}

.weather-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 12px;
}

.weather-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background: var(--primary-color-light);
  border-radius: 12px;
  border: 1px solid var(--border-glass);
  border-left: 3px solid var(--accent-color);
  transition: all 0.3s ease;
  gap: 12px;


}

.weather-item-icon {
  font-size: var(--font-size-2xl);
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.weather-item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.label {
  font-size: var(--font-size-2xl);
  color: var(--text-tertiary);
  font-weight: 500;
  line-height: 1.2;
}

.value {
  font-size: var(--font-size-xl);
  color: var(--text-primary);
  font-weight: 600;
  line-height: 1.2;
}

.record-row {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 0;
}

.record-text {
  flex: 1;
  font-size: var(--font-size-3xl);
  color: var(--primary-color);
  font-weight: 500;
  margin: 0;
  line-height: 1.5;
  text-align: center;
}

// 展开/收起箭头样式
.record-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 16px;
  cursor: pointer;
  color: var(--accent-color);
  transition: all 0.3s ease;

  svg {
    transition: transform 0.3s ease;

    &.rotated {
      transform: rotate(180deg);
    }
  }
}

// Section组件相关样式
.sections-container {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  gap: 0;
  animation: slideInDown 0.3s ease-out;

  // 确保Section组件在FloatingInfoCard中的样式适配
  :deep(.memory-section),
  :deep(.info-section),
  :deep(.reminder-section),
  :deep(.weather-section),
  :deep(.topic-section),
  :deep(.travel-section),
  :deep(.food-section),
  :deep(.expectation-section),
  :deep(.other-attributes-section) {
    // 调整边距以适配FloatingInfoCard
    margin-top: 16px;

    // 确保背景和边框与FloatingInfoCard风格一致
    background: var(--primary-color-light);
    border-left: 3px solid var(--accent-color);
    box-shadow: var(--shadow-accent);

    // 调整内边距
    padding: 18px;

    .section-header {
      .section-title {
        font-size: 28px; // 稍微减小字体以适配卡片
      }

      .section-icon {
        font-size: 26px;
      }
    }

    .section-content {
      font-size: 26px; // 调整内容字体大小
    }
  }

  // 第一个Section组件不需要上边距
  :deep(.memory-section:first-child) {
    margin-top: 0;
  }
}

.basic-info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.basic-info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 12px;
  background: var(--primary-color-medium);
  border-radius: 8px;
  border: 1px solid var(--border-glass);
}

.basic-info-key {
  font-size: var(--font-size-base);
  font-weight: 500;
  color: var(--text-tertiary);
  line-height: 1.2;
}

.basic-info-value {
  font-size: var(--font-size-lg);
  font-weight: 400;
  color: var(--text-primary);
  line-height: 1.3;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
</style>
