export function copyText(text: string): Promise<void> {
  if (typeof navigator !== 'undefined' && navigator?.clipboard?.writeText) {
    return navigator.clipboard.writeText(text)
  }
  const ta = document.createElement('textarea')
  ta.value = text
  ta.style.position = 'fixed'
  ta.style.opacity = '0'
  document.body.appendChild(ta)
  ta.select()
  try {
    document.execCommand('copy')
  } catch {
    // Ignore copy errors
  }
  document.body.removeChild(ta)
  return Promise.resolve()
}

export async function shareText(title: string, text: string): Promise<'shared' | 'copied'> {
  if (typeof navigator !== 'undefined' && 'share' in navigator) {
    try {
      await (navigator as { share: (data: { title: string; text: string }) => Promise<void> }).share({ title, text })
      return 'shared'
    } catch {
      // Ignore share errors
    }
  }
  await copyText(text)
  return 'copied'
}

export function useShare() {
  return {
    copyText,
    shareText,
    showDetailsPlaceholder() {
      console.log('"查看详情"功能稍后接入（对齐 iOS 的详情页）')
    },
    async copyWithToast(text: string) {
      await copyText(text)
      console.log('内容已复制到剪贴板')
    },
    async shareWithFallback(title: string, text: string) {
      const mode = await shareText(title, text)
      if (mode === 'copied') {
        console.log('已复制到剪贴板（系统分享稍后接入）')
      }
    }
  }
}
