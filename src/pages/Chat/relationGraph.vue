<template>
  <div class="relation-graph-container theme-background">
    <div class="relation-graph-page">
      <!-- 头部区域 - 显示弹窗时隐藏 -->
      <div v-show="!shouldHideMainComponents" class="header">
        <Maintopbar
          :show-back-btn="false"
          :show-history-btn="false"
          :show-relationship-btn="false"
          :show-user-avatar="false"
          :show-assistant-avatar="true"
          :assistant-avatar-src="selectedAssistantAvatar"
          :assistant-name="selectedAssistantName"
          :selected-assistant-avatar="selectedAssistantAvatar"
          :show-voice-btn="false"
          :show-person-list-btn="false"
          :show-add-chat-btn="false"
          :show-home-btn="true"
          :show-memo-btn="true"
          :show-back-to-index-btn="true"
          :show-feature-intro="true"
          :show-graph-tab-switch="false"
          :active-graph-tab="activeGraphTab"
          add-chat-type="add"
          :is-chat-play="isChatPlay"
          :user-loading="userLoading"
          :current-mis-id="currentMisId"
          :get-random-color="getRandomColor"
          :get-avatar-letter="getAvatarLetter"
          :show-header-grad="false"
          @history="toggleHistorySidebar"
          @voice="handleChatPlay"
          @relationship="handleRelationship"
          @add-chat="handleAddChat"
          @avatar-click="handleAvatarClick"
          @home="handleHome"
          @memo="handleMemo"
          @back-to-index="handleBackToIndex"
          @toggle-graph-tab="toggleGraphView"
        />
      </div>

      <!-- 搜索区域 - 显示弹窗时隐藏 -->
      <div v-show="!shouldHideMainComponents" class="search-area">
        <SearchBar
          :placeholder="'请输入姓名进行搜索'"
          :is-searching="isSearching"
          @search="handleSearch"
          @add="handleAddPerson"
          @input="handleSearchInput"
        />
      </div>

      <!-- 标签切换区域 - 显示弹窗时隐藏 -->
      <div v-show="!shouldHideMainComponents" class="tab-switch-area">
        <div class="tab-switch-container">
          <button
            :class="['tab-button', { active: activeGraphTab === 'graph' }]"
            @click="toggleGraphView()"
          >
            关系图
          </button>
          <button
            :class="['tab-button', { active: activeGraphTab === 'list' }]"
            @click="toggleGraphView()"
          >
            人员列表
          </button>
        </div>
      </div>

      <!-- EChart关系图区域 - 显示弹窗时隐藏 -->
      <div v-show="!shouldHideMainComponents" class="graph-container">
        <!-- 内容区域 -->
        <div class="graph-content-wrapper">
          <!-- 关系图视图 -->
          <div v-if="activeGraphTab === 'graph'" class="graph-view">
            <div v-if="loading" class="loading-container">
              <div class="loading-text">正在加载关系图数据...</div>
            </div>
            <div v-else-if="echartGraphData" class="graph-content">
              <!-- 当只有核心节点时显示欢迎界面 -->
              <div v-if="isOnlyCoreNode" class="welcome-container">
                <div class="welcome-content">
                  <div class="welcome-header">
                    <div class="welcome-icon">
                      <img
                        :src="selectedAssistantAvatar"
                        :alt="selectedAssistantName + '助手'"
                        class="assistant-avatar"
                      />
                    </div>
                    <div class="cyber-title">欢迎来到人际关系助手</div>
                    <div class="cyber-subtitle">
                      我是{{ selectedAssistantName }}，你的专属人际关系管理助手
                    </div>
                  </div>

                  <div class="welcome-features">
                    <div class="cyber-feature-card">
                      <div class="cyber-feature-icon">💬</div>
                      <div class="feature-text">
                        <div class="cyber-feature-title">智能对话</div>
                        <div class="cyber-feature-desc">与我聊天，分享你身边的人和事</div>
                      </div>
                    </div>
                    <div class="cyber-feature-card">
                      <div class="cyber-feature-icon">🕸️</div>
                      <div class="feature-text">
                        <div class="cyber-feature-title">关系网络</div>
                        <div class="cyber-feature-desc">自动构建你的人际关系图谱</div>
                      </div>
                    </div>
                    <div class="cyber-feature-card">
                      <div class="cyber-feature-icon">📝</div>
                      <div class="feature-text">
                        <div class="cyber-feature-title">随手记</div>
                        <div class="cyber-feature-desc">记住重要的人际互动和事件</div>
                      </div>
                    </div>
                  </div>

                  <div class="welcome-actions">
                    <button class="cyber-btn-primary" @click="handleStartChat">
                      <span class="btn-icon">💬</span>
                      开始聊天
                    </button>
                    <button class="cyber-btn-secondary" @click="handleCoreNodeClick">
                      <span class="btn-icon">👥</span>
                      管理联系人
                    </button>
                  </div>
                </div>
              </div>
              <!-- 当有多个节点时显示EChart关系图 -->
              <EchartRelationGraph
                v-else
                ref="echartRelationGraphRef"
                :data="echartGraphData"
                :width="'100%'"
                :height="'100%'"
                @node-click="handleNodeClick"
              />
            </div>
            <div v-else class="error-container">
              <div class="error-text">暂无关系图数据</div>
            </div>
          </div>

          <!-- 人员列表视图 -->
          <div v-else-if="activeGraphTab === 'list'" class="list-view">
            <PersonList
              ref="personListRef"
              :user-id="currentMisId"
              :search-results="personListSearchResults"
              :is-search-mode="isPersonListSearchMode"
              @refresh="handleRefreshRelationGraph"
              @show-person-detail="handlePersonListShowDetail"
              @edit-person="handlePersonListEditPerson"
            />
          </div>
        </div>
      </div>

      <!-- 底部提醒容器 - 空状态下隐藏，显示弹窗时也隐藏 -->
      <div
        v-if="(!isOnlyCoreNode || reminders.length > 0) && !shouldHideMainComponents"
        class="reminder-container"
      >
        <!-- 有提醒时显示提醒列表 -->
        <div v-if="reminders.length > 0" class="reminder-swiper-container">
          <swiper
            :slides-per-view="'auto'"
            :space-between="20"
            :centered-slides="false"
            :free-mode="true"
            :grab-cursor="true"
            class="reminder-swiper"
          >
            <!-- 添加提醒卡片 -->
            <swiper-slide class="reminder-slide">
              <AddReminderCard @click="handleAddReminderClick" />
            </swiper-slide>

            <swiper-slide
              v-for="reminder in reminders"
              :key="reminder.reminder_id"
              class="reminder-slide"
            >
              <ReminderItem
                :reminder-data="reminder"
                @click="handleReminderItemClick"
                @delete-request="handleReminderDeleteRequest"
              />
            </swiper-slide>
          </swiper>
        </div>

        <!-- 没有提醒时显示添加卡片 -->
        <div v-else-if="!loading && reminders.length === 0" class="empty-reminders-container">
          <div class="empty-reminders-content">
            <div class="empty-add-card">
              <AddReminderCard @click="handleAddReminderClick" />
            </div>
          </div>
        </div>
      </div>

      <!-- 底部输入框 - 参考 chat.vue 的样式 -->
      <div class="footer">
        <!-- 输入框 -->
        <form class="input-wrapper" action="" @submit.prevent="handleFormSubmit">
          <inputBar
            ref="inputBarRef"
            @voice-send="handleInputSend"
            @get-started="handleGetStarted"
            @send="handleInputSend"
            @stop="handleStop"
            @recording-status="handleRecordingStatus"
          />
        </form>

        <!-- 老董假装说话样式 - 直接放在输入框下方 -->
        <div class="laodong-fake-speaking">
          <div class="fake-speaking-container">
            <div class="laodong-avatar">
              <img :src="fakeSpeakingAssistantAvatar" alt="老董头像" />
            </div>
            <div class="fake-speaking-content">
              <div class="fake-speaking-text">老董会根据您的问题给出专业建议</div>
              <div class="fake-speaking-dots">
                <span class="dot"></span>
                <span class="dot"></span>
                <span class="dot"></span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 对话展示组件 - 位于页面内部 -->
      <ChatDialog
        :visible="showChatDialog"
        :messages="chatMessages"
        :conversation-id="conversationId"
        :user-id="currentMisId"
        @close="handleCloseChatDialog"
        @send-message="handleChatDialogSend"
        @regenerate="handleRegenerate"
        @new-chat="clearChatSession"
      />

      <!-- 人员详情弹窗 -->
      <PersonDetailPopup
        v-if="showPersonDetailPopup"
        ref="personDetailPopupRef"
        :user-id="currentMisId"
        :person-id="selectedPersonId"
        :person-name="selectedPersonName"
        :is-user-profile="selectedIsUserProfile"
        @close="closePersonDetailPopup"
        @refresh="handleRefreshRelationGraph"
        @show-add-event-dialog="handleShowPersonDetailAddEventDialog"
        @show-edit-event-dialog="handleShowPersonDetailEditEventDialog"
        @show-add-reminder-dialog="handleShowPersonDetailAddReminderDialog"
        @show-edit-reminder-dialog="handleShowPersonDetailEditReminderDialog"
        @show-natural-edit-reminder-dialog="handleShowPersonDetailNaturalEditReminderDialog"
        @delete-event="handleEventDeleteRequest"
        @delete-person="handlePersonDeleteRequest"
        @delete-reminder="handleReminderDeleteRequest"
        @delete-lifestyle-item="handleLifestyleItemDeleteRequest"
        @avatar-click="handlePersonAvatarClick"
      />

      <!-- 添加人员弹窗 -->
      <PersonAddDialog
        v-if="showAddPersonDialog"
        :user-id="currentMisId"
        @close="closeAddPersonDialog"
        @success="handleAddPersonSuccess"
      />

      <!-- 添加提醒弹窗 -->
      <AddReminderDialog
        :show="showAddReminderDialog"
        :user-id="currentMisId"
        :edit-reminder="reminderToEdit"
        @close="handleCloseAddReminderDialog"
        @success="handleReminderAddSuccess"
      />

      <!-- 编辑提醒弹窗 -->
      <EditReminderDialog
        :show="showEditReminderDialog"
        :user-id="currentMisId"
        :reminder-data="reminderToEditWithNaturalLanguage"
        @close="handleCloseEditReminderDialog"
        @success="handleReminderEditSuccess"
      />

      <!-- 编辑人员弹窗 -->
      <PersonEditDialog
        v-if="showPersonEditDialog"
        :person="personToEdit"
        :user-id="currentMisId"
        @close="closePersonEditDialog"
        @success="handlePersonEditSuccess"
      />

      <!-- PersonDetailPopup内部的二级弹窗 -->
      <!-- 添加事件弹窗 -->
      <AddEventDialog
        :show="showPersonDetailAddEventDialog"
        :user-id="currentMisId"
        :person-id="personDetailPersonId"
        @close="showPersonDetailAddEventDialog = false"
        @success="handlePersonDetailAddEventSuccess"
      />

      <!-- 编辑事件弹窗 -->
      <EventDirectEdit
        :show="showPersonDetailEditEventDialog"
        :event-data="personDetailEventData"
        @close="showPersonDetailEditEventDialog = false"
        @save="handlePersonDetailEditEventSuccess"
      />

      <!-- PersonDetailPopup的添加提醒弹窗 -->
      <AddReminderDialog
        :show="showPersonDetailAddReminderDialog"
        :user-id="currentMisId"
        :person-id="personDetailPersonId"
        @close="showPersonDetailAddReminderDialog = false"
        @success="handlePersonDetailAddReminderSuccess"
      />

      <!-- PersonDetailPopup的自然语言编辑提醒弹窗 -->
      <EditReminderDialog
        :show="showPersonDetailNaturalEditReminderDialog"
        :user-id="currentMisId"
        :reminder-data="personDetailReminderData"
        @close="showPersonDetailNaturalEditReminderDialog = false"
        @success="handlePersonDetailNaturalEditReminderSuccess"
      />
    </div>

    <!-- 删除确认弹窗 - 统一处理删除提醒、事件、人员、生活方式项目 -->
    <DeleteConfirmDialog
      :visible="showDeleteReminderDialog || showDeleteEventDialog || showDeletePersonDialog || showDeleteLifestyleItemDialog"
      :content="deleteDialogContent"
      :hint="deleteDialogHint"
      :is-loading="isDeleting"
      @confirm="confirmDelete"
      @cancel="closeDeleteDialog"
    />

    <!-- 历史对话侧边栏 -->
    <HistorySidebar
      :is-open="showHistorySidebar"
      :current-title="''"
      @close="handleCloseHistorySidebar"
      @select-conversation="handleSelectConversation"
    />

    <!-- 备忘录侧边栏 -->
    <MemoSidebar :is-open="showMemoSidebar" @close="handleCloseMemoSidebar" />

    <!-- 头像选择弹窗 -->
    <AvatarSelectionDialog
      v-if="showAvatarSelectionDialog"
      @close="showAvatarSelectionDialog = false"
      @select-avatar="handleAvatarSelect"
      @upload-avatar="handleUploadAvatar"
    />

    <!-- 隐藏的头像上传组件 -->
    <div style="display: none">
      <AvatarUpload
        ref="avatarUploadRef"
        v-model="hiddenAvatarValue"
        :size="50"
        placeholder="上传头像"
        :max-size="10"
        @upload-success="handleAvatarUploadSuccess"
        @upload-error="handleAvatarUploadError"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, nextTick } from 'vue';
import { useRouter } from 'vue-router';
import { Swiper, SwiperSlide } from 'swiper/vue';
import 'swiper/css';
import 'swiper/css/free-mode';
import { showFailToast, showSuccessToast } from 'vant';
import Maintopbar from '@/components/Maintopbar.vue';
import EchartRelationGraph from '@/components/RelationshipGraph/EChartRelationGraph.vue';
import ReminderItem from '@/components/Events/reminderItem.vue';
import AddReminderCard from '@/components/Events/addReminderCard.vue';
import PersonDetailPopup from '@/components/Persons/PersonDetailPopup.vue';
import PersonList from '@/components/Persons/personList.vue';
import AvatarSelectionDialog from '@/components/Common/AvatarSelectionDialog.vue';
import AvatarUpload from '@/components/Common/AvatarUpload.vue';
import PersonAddDialog from '@/components/Persons/PersonAddDialog.vue';
import PersonEditDialog from '@/components/Persons/PersonEditDialog.vue';
import AddEventDialog from '@/components/Dialogs/AddEventDialog.vue';
import EventDirectEdit from '@/components/Dialogs/EventDirectEdit.vue';
import HistorySidebar from '@/components/Chat/historySidebar.vue';
import MemoSidebar from '@/components/Chat/memoSidebar.vue';
import AddReminderDialog from '@/components/Dialogs/AddReminderDialog.vue';
import EditReminderDialog from '@/components/Dialogs/EditReminderDialog.vue';
import SearchBar from '@/components/Common/SearchBar.vue';
import inputBar from '@/components/Chat/inputBar.vue';
import ChatDialog from '@/components/Dialogs/ChatDialog.vue';
import DeleteConfirmDialog from '@/components/Common/DeleteConfirmDialog.vue';
import {
  getRelationGraphData,
  getPersons,
  getUserProfile,
  searchPerson,
  deletePerson,
  updatePerson,
  type IGraphData,
  type IPersonData,
} from '@/apis/relation';
// 类型已在全局定义，无需导入
import { getUserInfo } from '@/apis/common';
import { getReminders, deleteReminder, deletePersonEvent, getPersonDetail, type IReminder, type IEvent } from '@/apis/memory';
import { streamChat, createConversation, type IToolCall } from '@/apis/chat';
import { Typewriter } from '@/utils/typeWriter';
import { useChatStore } from '@/stores/chat';
import { useThemeStore } from '@/stores/theme';
import { useUserStore } from '@/stores/user';
import { AnswerStatusEnum } from '@/constants/chat';
// 导入头像图片 - 统一使用首页的头像文件
import avatar1 from '@/assets/laodong/cat.png';
import avatar2 from '@/assets/laodong/dog.png';
import avatar3 from '@/assets/laodong/man.png';
import avatar4 from '@/assets/laodong/man2.png';
import avatar5 from '@/assets/laodong/woman.png';
import avatar6 from '@/assets/laodong/woman2.png';

const router = useRouter();
const userStore = useUserStore();

// Echart关系图数据接口
interface IEchartNode {
  id: string;
  label: string;
  originalId: string; // 保存原始ID用于边的映射
  type: 'core' | 'other';
  person_id?: string; // 添加person_id字段用于API调用
  avatar?: string; // 添加avatar字段用于显示头像
}

interface IEchartEdge {
  source: string;
  target: string;
  label: string;
}

interface IEchartRelationshipData {
  nodes: IEchartNode[];
  edges: IEchartEdge[];
}

// 图形尺寸
const graphWidth = ref(800);
const graphHeight = ref(600);

// 关系图数据
const graphData = ref<IGraphData | null>(null);
const echartGraphData = ref<IEchartRelationshipData | null>(null);
const personsData = ref<IPersonData[]>([]); // 保存原始的persons数据
const loading = ref(true);
const currentMisId = ref<string>('');

// 从index.vue迁移过来的变量
const isChatPlay = ref(false);
const showHistorySidebar = ref(false);
const showMemoSidebar = ref(false);
const userLoading = ref(false);

// 人员详情弹窗相关状态
const showPersonDetailPopup = ref(false);
const selectedPersonId = ref<string>('');
const selectedPersonName = ref<string>('');
const selectedIsUserProfile = ref<boolean>(false);
const personDetailPopupRef = ref<InstanceType<typeof PersonDetailPopup> | null>(null);

// Tab切换相关状态
const activeGraphTab = ref<'graph' | 'list'>('graph');

// 提醒添加弹窗相关状态
const showAddReminderDialog = ref(false);
const reminderToEdit = ref<IReminder | null>(null);

// 提醒编辑弹窗相关状态
const showEditReminderDialog = ref(false);
const reminderToEditWithNaturalLanguage = ref<IReminder | null>(null);

// 删除确认弹窗相关状态
const showDeleteReminderDialog = ref(false);
const showDeleteEventDialog = ref(false);
const showDeletePersonDialog = ref(false);
const showDeleteLifestyleItemDialog = ref(false);
const reminderToDelete = ref<IReminder | null>(null);
const eventToDelete = ref<IEvent | null>(null);
const personToDelete = ref<IPersonData | null>(null);
const lifestyleItemToDelete = ref<{ type: string; content: string; category: string; index: number } | null>(null);
const isDeleting = ref(false);

// 删除对话框内容计算属性
const deleteDialogContent = computed(() => {
  if (showDeleteReminderDialog.value) return '确定要删除这个提醒事项吗？';
  if (showDeleteEventDialog.value) return '确定要删除这个事件吗？';
  if (showDeletePersonDialog.value) return '确定要删除这个人员吗？';
  if (showDeleteLifestyleItemDialog.value) return `确定要删除这个${lifestyleItemToDelete.value?.type}吗？`;
  return '';
});

// 删除对话框提示计算属性
const deleteDialogHint = computed(() => {
  if (showDeleteReminderDialog.value) return reminderToDelete.value?.display_text || '提醒事项';
  if (showDeleteEventDialog.value) return eventToDelete.value?.description_text || '事件';
  if (showDeletePersonDialog.value) return personToDelete.value?.canonical_name || '人员';
  if (showDeleteLifestyleItemDialog.value) return lifestyleItemToDelete.value?.content || '项目';
  return '';
});

// 搜索相关状态
const isSearching = ref(false);

// 人员列表搜索相关状态
const personListSearchResults = ref<IPersonData[]>([]);
const isPersonListSearchMode = ref(false);

// PersonList组件引用
const personListRef = ref<{ loadPersons: () => Promise<void> } | null>(null);

// 添加人员弹窗相关状态
const showAddPersonDialog = ref(false);

// 编辑人员弹窗相关状态
const showPersonEditDialog = ref(false);
const personToEdit = ref<IPersonData | null>(null);

// 头像选择弹窗相关状态
const showAvatarSelectionDialog = ref(false);
const avatarUploadRef = ref();
const hiddenAvatarValue = ref('');
const currentPersonForAvatar = ref<IPersonData | null>(null);

// 计算属性：判断是否有一级弹窗显示
const hasFirstLevelPopup = computed(() => {
  return showPersonDetailPopup.value || showAddPersonDialog.value || showPersonEditDialog.value;
});

// PersonDetailPopup内部二级弹窗的具体状态
const showPersonDetailAddEventDialog = ref(false);
const showPersonDetailEditEventDialog = ref(false);
const showPersonDetailAddReminderDialog = ref(false);
const showPersonDetailEditReminderDialog = ref(false);
const showPersonDetailNaturalEditReminderDialog = ref(false);

// 二级弹窗相关数据
const personDetailEventData = ref<IEvent | null>(null);
const personDetailReminderData = ref<IReminder | null>(null);
const personDetailPersonId = ref<string>('');

// 计算属性：判断是否应该隐藏主要组件
const shouldHideMainComponents = computed(() => {
  return hasFirstLevelPopup.value;
});

// 提醒数据
const reminders = ref<IReminder[]>([]);

// 对话相关状态
const showChatDialog = ref(false);
const chatMessages = ref<IChatStreamContent[]>([]);
const conversationId = ref('');
const isRecording = ref(false);
const inputBarRef = ref<InstanceType<typeof inputBar> | null>(null);

// 聊天相关状态
const chatStore = useChatStore();
const themeStore = useThemeStore();
const isStoppedByUser = ref(false);
const streamController = ref<AbortController | null>(null);
const isTypewriterStarted = ref(false);

// 创建打字机实例
const typewriter = new Typewriter(
  (str: string) => {
    if (str && chatMessages.value.length > 0) {
      const lastMessage = chatMessages.value[chatMessages.value.length - 1];
      if (lastMessage && lastMessage.role === 'assistant') {
        lastMessage.content = str;
      }
    }
  },
  () => {
    // 聊天完成回调
    console.log('✅ [relationGraph.vue] 聊天完成');
    chatStore.setAnswerStatus(AnswerStatusEnum.SUCCESS);
  },
);

// AI助理头像选择的存储键
const AI_ASSISTANT_STORAGE_KEY = 'selectedAssistantIndex';

// AI助手数据
const assistants = ref([
  {
    id: 1,
    name: '老董',
    avatar: avatar1,
  },
  {
    id: 2,
    name: '老董',
    avatar: avatar2,
  },
  {
    id: 3,
    name: '老董',
    avatar: avatar3,
  },
  {
    id: 4,
    name: '老董',
    avatar: avatar4,
  },
  {
    id: 5,
    name: '老董',
    avatar: avatar5,
  },
  {
    id: 6,
    name: '老董',
    avatar: avatar6,
  },
]);

// 立即从localStorage获取初始索引，避免跳变
const getInitialAssistantIndex = (): number => {
  const savedIndex = localStorage.getItem(AI_ASSISTANT_STORAGE_KEY);
  const index = savedIndex ? parseInt(savedIndex, 10) : 0;
  console.log('🔍 [relationGraph.vue] 获取初始助手索引:', {
    savedIndex,
    index,
  });
  if (index >= 0 && index < 6) {
    // 助手数组长度为6
    console.log('✅ [relationGraph.vue] 使用保存的助手索引:', index);
    return index;
  }
  console.log('⚠️ [relationGraph.vue] 使用默认助手索引: 0');
  return 0;
};

// 响应式的助手索引，立即初始化为正确值
const currentAssistantIndex = ref(getInitialAssistantIndex());

// 获取选中的助手头像 - 在关系图页面中强制显示"董关系"的头像
const selectedAssistantAvatar = computed(() => {
  // 关系图页面永远使用 avatar3 作为"董关系"的头像
  console.log('✅ [relationGraph.vue] 关系图页面使用董关系头像:', avatar3);
  return avatar3;
});

// 获取选中的助手名称 - 在关系图页面中强制显示"董关系"
const selectedAssistantName = computed(() => {
  return '董关系'; // 关系图页面永远显示"董关系"
});

// 获取底部假装说话容器的助手头像 - 使用用户在首页选择的头像
const fakeSpeakingAssistantAvatar = computed(() => {
  const index = currentAssistantIndex.value;
  if (index >= 0 && index < assistants.value.length) {
    console.log(
      '✅ [relationGraph.vue] 底部假装说话容器使用助手头像:',
      assistants.value[index].name,
      assistants.value[index].avatar,
    );
    return assistants.value[index].avatar;
  }
  console.log(
    '⚠️ [relationGraph.vue] 底部假装说话容器使用默认助手头像:',
    assistants.value[0].name,
    assistants.value[0].avatar,
  );
  return assistants.value[0].avatar;
});

// 计算属性：检查是否只有核心节点
const isOnlyCoreNode = computed(() => {
  if (!echartGraphData.value || !echartGraphData.value.nodes) {
    return false;
  }
  // 检查是否只有一个节点且为核心节点
  const realNodes = echartGraphData.value.nodes.filter(
    (node) => node.type === 'core' || node.type === 'other',
  );
  return realNodes.length === 1 && realNodes[0].type === 'core';
});

// 将relation.ts的IGraphData转换为Echart关系图数据格式
const transformToEchartData = (
  relationData: IGraphData,
  coreUserId: string,
  personsDataParam?: IPersonData[],
  userProfile?: IPersonData,
): IEchartRelationshipData => {
  const nodes: IEchartNode[] = [];
  const edges: IEchartEdge[] = [];

  console.log('🔄 [relationGraph.vue] transformToEchartData 开始转换数据:', {
    relationDataNodes: relationData.nodes.length,
    relationDataLinks: relationData.links.length,
    coreUserId,
    personsDataLength: personsDataParam?.length || 0,
    userProfileCanonicalName: userProfile?.canonical_name,
  });

  // 创建canonical_name到person_id和avatar的映射
  const nameToPersonIdMap = new Map<string, string>();
  const nameToAvatarMap = new Map<string, string>();

  // 如果有用户档案信息，先添加核心用户的映射
  if (userProfile) {
    // 使用canonical_name作为键
    nameToPersonIdMap.set(userProfile.canonical_name, userProfile.person_id);
    if (userProfile.avatar) {
      nameToAvatarMap.set(userProfile.canonical_name, userProfile.avatar);
    }

    // 同时使用coreUserId作为键，确保能够正确查找到
    nameToPersonIdMap.set(coreUserId, userProfile.person_id);
    if (userProfile.avatar) {
      nameToAvatarMap.set(coreUserId, userProfile.avatar);
    }

    console.log('📋 [relationGraph.vue] 添加核心用户映射:', {
      canonical_name: userProfile.canonical_name,
      coreUserId,
      person_id: userProfile.person_id,
      avatar: userProfile.avatar,
      mappingKeys: [userProfile.canonical_name, coreUserId],
    });
  }

  if (personsDataParam) {
    personsDataParam.forEach((person) => {
      nameToPersonIdMap.set(person.canonical_name, person.person_id);
      if (person.avatar) {
        nameToAvatarMap.set(person.canonical_name, person.avatar);
      }
    });
    console.log(
      '📋 [relationGraph.vue] canonical_name到person_id映射:',
      Array.from(nameToPersonIdMap.entries()),
    );
    console.log(
      '📋 [relationGraph.vue] canonical_name到avatar映射:',
      Array.from(nameToAvatarMap.entries()),
    );
  } else {
    console.warn('⚠️ [relationGraph.vue] personsDataParam为空，无法创建映射');
  }

  // 用于处理重复节点ID的Map，记录每个原始ID出现的次数
  const idCountMap = new Map<string, number>();
  const duplicateNodeIds = new Set<string>();

  // 首先统计每个ID出现的次数
  relationData.nodes.forEach((node) => {
    const count = idCountMap.get(node.id) || 0;
    idCountMap.set(node.id, count + 1);
    if (count > 0) {
      duplicateNodeIds.add(node.id);
    }
  });

  // 如果发现重复节点，记录详细信息
  if (duplicateNodeIds.size > 0) {
    console.warn(
      '⚠️ [relationGraph.vue] 检测到重复的节点ID，将为重复节点添加唯一后缀:',
      Array.from(duplicateNodeIds),
    );
    console.log('📊 [relationGraph.vue] 重复ID统计:', Object.fromEntries(idCountMap.entries()));
  }

  // 用于跟踪每个原始ID已经使用的次数
  const usedCountMap = new Map<string, number>();

  // 转换节点数据
  relationData.nodes.forEach((node) => {
    // 为重复的节点ID生成唯一的ID
    let uniqueId = node.id;
    const originalId = node.id;
    const usedCount = usedCountMap.get(originalId) || 0;

    if (usedCount > 0) {
      // 为重复节点添加后缀，从第二个开始
      uniqueId = `${originalId}_${usedCount}`;
      console.log(`🔄 [relationGraph.vue] 为重复节点生成唯一ID: ${originalId} -> ${uniqueId}`);
    }

    usedCountMap.set(originalId, usedCount + 1);

    const isCore = originalId === coreUserId; // 使用原始ID判断是否为核心用户
    const personId = nameToPersonIdMap.get(originalId); // 使用原始ID查找person_id
    const avatar = nameToAvatarMap.get(originalId); // 使用原始ID查找avatar

    console.log('🔄 [relationGraph.vue] 处理节点:', {
      originalId,
      uniqueId,
      isCore,
      personId,
      hasPersonId: !!personId,
      avatar,
      coreUserId,
      avatarMapKeys: Array.from(nameToAvatarMap.keys()),
      avatarMapHasOriginalId: nameToAvatarMap.has(originalId),
    });

    // 对于核心节点，优先使用用户档案中的canonical_name，否则使用originalId
    let displayLabel = originalId;
    if (isCore && userProfile && userProfile.canonical_name) {
      displayLabel = userProfile.canonical_name;
      console.log('🔄 [relationGraph.vue] 核心节点使用canonical_name作为显示名称:', displayLabel);
    }

    nodes.push({
      id: uniqueId, // 使用唯一ID
      label: displayLabel, // 核心节点显示canonical_name，其他节点显示原始ID
      originalId, // 保存原始ID用于边的映射
      type: isCore ? 'core' : 'other',
      person_id: personId, // 添加person_id信息
      avatar, // 添加avatar信息
    });
  });

  // 创建原始ID到唯一ID的映射
  const originalIdToUniqueIdMap = new Map<string, string[]>();
  nodes.forEach((node) => {
    const { originalId } = node; // 使用保存的原始ID
    if (!originalIdToUniqueIdMap.has(originalId)) {
      originalIdToUniqueIdMap.set(originalId, []);
    }
    originalIdToUniqueIdMap.get(originalId)!.push(node.id);
  });

  // 用于检测重复边
  const seenEdges = new Set<string>();

  // 转换边数据
  relationData.links.forEach((link) => {
    // 获取源节点和目标节点的所有唯一ID
    const sourceUniqueIds = originalIdToUniqueIdMap.get(link.source) || [];
    const targetUniqueIds = originalIdToUniqueIdMap.get(link.target) || [];

    if (sourceUniqueIds.length === 0 || targetUniqueIds.length === 0) {
      console.warn('⚠️ [relationGraph.vue] 边引用了不存在的节点:', {
        source: link.source,
        target: link.target,
        sourceExists: sourceUniqueIds.length > 0,
        targetExists: targetUniqueIds.length > 0,
      });
      return;
    }

    // 为每个源节点和目标节点的组合创建边
    sourceUniqueIds.forEach((sourceId) => {
      targetUniqueIds.forEach((targetId) => {
        // 创建边的唯一标识符（双向边视为同一条边）
        const edgeKey = [sourceId, targetId].sort().join('-');
        if (seenEdges.has(edgeKey)) {
          return; // 跳过重复的边
        }
        seenEdges.add(edgeKey);

        edges.push({
          source: sourceId,
          target: targetId,
          label: '关系', // 简化关系标签
        });
      });
    });
  });

  console.log('✅ [relationGraph.vue] 数据转换完成:', {
    原始节点数量: relationData.nodes.length,
    原始边数量: relationData.links.length,
    有效节点数量: nodes.length,
    有效边数量: edges.length,
    重复节点数量: duplicateNodeIds.size,
    重复节点ID: Array.from(duplicateNodeIds),
    nodesWithPersonId: nodes.filter((n) => n.person_id).length,
    nodesWithoutPersonId: nodes.filter((n) => !n.person_id).length,
    节点ID映射: Object.fromEntries(originalIdToUniqueIdMap.entries()),
  });

  return {
    nodes,
    edges,
  };
};

// 获取提醒数据
const loadReminders = async (userId: string) => {
  try {
    console.log('🔄 [relationGraph.vue] 开始获取提醒数据...');
    const response = await getReminders({ user_id: userId });
    console.log('📡 [relationGraph.vue] 提醒数据:', response);

    if (response && response.success) {
      reminders.value = (response.reminders || []).reverse();
      console.log('✅ [relationGraph.vue] 提醒数据加载成功，共', reminders.value.length, '条提醒');
    } else {
      console.warn('⚠️ [relationGraph.vue] 提醒数据格式异常');
      reminders.value = [];
    }
  } catch (error) {
    console.error('❌ [relationGraph.vue] 获取提醒数据失败:', error);
    // 如果API调用失败，不显示错误提示，只是保持空数组
    reminders.value = [];
  }
};

// 获取用户信息和关系图数据
const loadRelationData = async () => {
  try {
    loading.value = true;
    console.log('🔄 [relationGraph.vue] 开始获取用户信息...');

    // 获取用户信息
    const userInfo = await getUserInfo();
    console.log('📡 [relationGraph.vue] 用户信息:', userInfo);

    if (userInfo && userInfo.login) {
      currentMisId.value = userInfo.login;
      userStore.userInfo = userInfo; // 设置 userStore，确保其他组件能获取到用户信息
      console.log('✅ [relationGraph.vue] 用户信息加载成功, misId:', currentMisId.value);
    } else {
      console.warn('⚠️ [relationGraph.vue] 用户信息格式异常');
      currentMisId.value = 'unknown_user';
    }

    // 获取关系图数据
    console.log('🔄 [relationGraph.vue] 开始获取关系图数据...');

    // 先获取原始的persons数据
    const personsResponse = await getPersons({
      userId: currentMisId.value,
      limit: 100,
      offset: 0,
    });

    if (personsResponse.result === 'success' && personsResponse.persons) {
      personsData.value = personsResponse.persons;
      console.log('✅ [relationGraph.vue] 原始persons数据加载成功:', personsData.value);
    }

    // 获取用户档案信息（包含核心用户的头像）
    let userProfile: IPersonData | undefined;
    try {
      const userProfileResponse = await getUserProfile({
        user_id: currentMisId.value,
      });
      if (userProfileResponse.result === 'success' && userProfileResponse.person) {
        userProfile = userProfileResponse.person;
        console.log('✅ [relationGraph.vue] 用户档案加载成功:', userProfile);
      }
    } catch (error) {
      console.warn('⚠️ [relationGraph.vue] 获取用户档案失败:', error);
    }

    const relationData = await getRelationGraphData(currentMisId.value);
    console.log('📡 [relationGraph.vue] 关系图数据:', relationData);

    graphData.value = relationData;

    // 转换为Echart关系图数据格式，传入原始persons数据和用户档案
    echartGraphData.value = transformToEchartData(
      relationData,
      currentMisId.value,
      personsData.value,
      userProfile,
    );
    console.log('✅ [relationGraph.vue] 关系图数据加载成功，Echart数据:', echartGraphData.value);

    // 获取提醒数据
    await loadReminders(currentMisId.value);
  } catch (error) {
    console.error('❌ [relationGraph.vue] 获取关系图数据失败:', error);
    showFailToast('获取关系图数据失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};

// 处理添加提醒卡片点击
const handleAddReminderClick = () => {
  reminderToEdit.value = null; // 清空编辑状态，进入添加模式
  showAddReminderDialog.value = true;
};

// 处理提醒项点击 - 进入自然语言编辑模式
const handleReminderItemClick = (reminder: IReminder) => {
  console.log('🔄 [relationGraph.vue] 提醒项点击，进入自然语言编辑模式:', reminder);
  reminderToEditWithNaturalLanguage.value = reminder; // 设置要编辑的提醒数据
  showEditReminderDialog.value = true;
};

// 处理提醒删除请求
const handleReminderDeleteRequest = (reminder: IReminder) => {
  console.log('🔄 [relationGraph.vue] 收到删除提醒请求:', reminder);
  reminderToDelete.value = reminder;
  showDeleteReminderDialog.value = true;
};

// 处理事件删除请求
const handleEventDeleteRequest = (event: IEvent) => {
  console.log('🔄 [relationGraph.vue] 收到删除事件请求:', event);
  eventToDelete.value = event;
  showDeleteEventDialog.value = true;
};

// 处理人员删除请求
const handlePersonDeleteRequest = (person: IPersonData) => {
  console.log('🔄 [relationGraph.vue] 收到删除人员请求:', person);
  personToDelete.value = person;
  showDeletePersonDialog.value = true;
};

// 处理生活方式项目删除请求
const handleLifestyleItemDeleteRequest = (itemInfo: { type: string; content: string; category: string; index: number }) => {
  console.log('🔄 [relationGraph.vue] 收到删除生活方式项目请求:', itemInfo);
  lifestyleItemToDelete.value = itemInfo;
  showDeleteLifestyleItemDialog.value = true;
};

// 统一的关闭删除确认弹窗
const closeDeleteDialog = () => {
  showDeleteReminderDialog.value = false;
  showDeleteEventDialog.value = false;
  showDeletePersonDialog.value = false;
  showDeleteLifestyleItemDialog.value = false;
  reminderToDelete.value = null;
  eventToDelete.value = null;
  personToDelete.value = null;
  lifestyleItemToDelete.value = null;
};

// 关闭删除提醒确认弹窗（保持向后兼容）
// const closeDeleteReminderDialog = () => {
//   closeDeleteDialog();
// };

// 统一的删除确认函数
const confirmDelete = async () => {
  if (isDeleting.value) return;

  try {
    isDeleting.value = true;

    if (showDeleteReminderDialog.value && reminderToDelete.value) {
      await confirmDeleteReminder();
    } else if (showDeleteEventDialog.value && eventToDelete.value) {
      await confirmDeleteEvent();
    } else if (showDeletePersonDialog.value && personToDelete.value) {
      await confirmDeletePerson();
    } else if (showDeleteLifestyleItemDialog.value && lifestyleItemToDelete.value) {
      await confirmDeleteLifestyleItem();
    }
  } catch (error) {
    console.error('❌ [relationGraph.vue] 删除操作失败:', error);
    showFailToast('删除失败');
  } finally {
    isDeleting.value = false;
  }
};

// 确认删除提醒
const confirmDeleteReminder = async () => {
  if (!reminderToDelete.value) return;

  console.log('🔄 [relationGraph.vue] 开始删除提醒...', {
    userId: currentMisId.value,
    reminderId: reminderToDelete.value.reminder_id,
  });

  const response = await deleteReminder({
    user_id: currentMisId.value,
    reminder_id: reminderToDelete.value.reminder_id,
  });

  console.log('📡 [relationGraph.vue] 删除提醒响应:', response);

  if (response && response.success) {
    console.log('✅ [relationGraph.vue] 提醒删除成功');
    showSuccessToast('删除成功');

    // 从列表中移除已删除的提醒
    reminders.value = reminders.value.filter(
      (r) => r.reminder_id !== reminderToDelete.value!.reminder_id,
    );

    // 关闭删除对话框
    closeDeleteDialog();

    // 延迟1100ms后刷新PersonDetailPopup中的提醒数据，确保ES数据库已完成删除
    setTimeout(async () => {
      console.log('🔄 [relationGraph.vue] 延迟刷新关系图数据（删除提醒成功）');
      if (showPersonDetailPopup.value && personDetailPopupRef.value) {
        // 通过刷新关系图数据来触发PersonDetailPopup的数据更新
        await loadRelationData();
      }
    }, 1100);
  } else {
    console.warn('⚠️ [relationGraph.vue] 删除提醒失败:', response);
    showFailToast(`删除失败：${response?.message || '未知错误'}`);
  }
};

// 确认删除事件
const confirmDeleteEvent = async () => {
  if (!eventToDelete.value) return;

  console.log('🔄 [relationGraph.vue] 开始删除事件...', {
    userId: currentMisId.value,
    eventId: eventToDelete.value.event_id,
  });

  const response = await deletePersonEvent({
    user_id: currentMisId.value,
    event_id: eventToDelete.value.event_id,
  });

  console.log('📡 [relationGraph.vue] 删除事件响应:', response);

  if (response && response.result === 'success') {
    console.log('✅ [relationGraph.vue] 事件删除成功');
    showSuccessToast('删除成功');

    // 关闭删除对话框
    closeDeleteDialog();

    // 延迟1100ms后刷新关系图数据，确保ES数据库已完成删除
    setTimeout(async () => {
      console.log('🔄 [relationGraph.vue] 延迟刷新关系图数据（删除事件成功）');
      await loadRelationData();
    }, 1100);

    // 刷新PersonDetailPopup中的MemorySection数据
    setTimeout(() => {
      if (personDetailPopupRef.value) {
        console.log('🔄 [relationGraph.vue] 延迟刷新PersonDetailPopup中的MemorySection数据（删除事件成功）');
        personDetailPopupRef.value.refreshMemoryData();
      }
    }, 1100);
  } else {
    console.warn('⚠️ [relationGraph.vue] 删除事件失败:', response);
    showFailToast(`删除失败：${response?.reason || '未知错误'}`);
  }
};

// 确认删除人员
const confirmDeletePerson = async () => {
  if (!personToDelete.value) return;

  console.log('🔄 [relationGraph.vue] 开始删除人员...', {
    userId: currentMisId.value,
    personId: personToDelete.value.person_id,
    name: personToDelete.value.canonical_name,
  });

  const response = await deletePerson(currentMisId.value, personToDelete.value.person_id);

  console.log('📡 [relationGraph.vue] 删除人员响应:', response);

  if (response && response.result === 'success') {
    console.log('✅ [relationGraph.vue] 人员删除成功');
    showSuccessToast('删除成功');

    // 关闭删除对话框
    closeDeleteDialog();

    // 刷新关系图数据
    await loadRelationData();
  } else {
    console.warn('⚠️ [relationGraph.vue] 删除人员失败:', response);
    showFailToast('删除人员失败');
  }
};

// 确认删除生活方式项目
const confirmDeleteLifestyleItem = async () => {
  if (!lifestyleItemToDelete.value) return;

  console.log('🔄 [relationGraph.vue] 开始删除生活方式项目...', {
    userId: currentMisId.value,
    itemInfo: lifestyleItemToDelete.value,
  });

  try {
    // 获取当前人员的详细信息
    const personDetailResponse = await getPersonDetail({
      user_id: currentMisId.value,
      person_id: selectedPersonId.value,
    });

    if (!personDetailResponse || personDetailResponse.result !== 'success') {
      throw new Error('获取人员信息失败');
    }

    const personDetail = personDetailResponse.person;
    const attributes = { ...(personDetail.key_attributes as Record<string, string>) };
    const { category, index } = lifestyleItemToDelete.value;

    if (category === 'travel') {
      // 删除旅行记录
      const travelHistory = attributes['旅游历史'];
      if (travelHistory) {
        const travelList = travelHistory.split('|').map(item => item.trim()).filter(item => item);
        travelList.splice(index, 1);
        if (travelList.length > 0) {
          attributes['旅游历史'] = travelList.join('|');
        } else {
          delete attributes['旅游历史'];
        }
      }
    } else if (category === 'food') {
      // 删除饮食偏好
      const foodPreferences = attributes['餐饮偏好'];
      if (foodPreferences) {
        const foodList = foodPreferences.split('|').map(item => item.trim()).filter(item => item);
        foodList.splice(index, 1);
        if (foodList.length > 0) {
          attributes['餐饮偏好'] = foodList.join('|');
        } else {
          delete attributes['餐饮偏好'];
        }
      }
    } else if (category === 'expectation') {
      // 删除期望
      const expectationKeys = ['期望', '我的期望', '期待', '愿望', '目标'];
      const foundKey = expectationKeys.find(key => attributes[key]);
      if (foundKey) {
        const expectationValue = attributes[foundKey];
        const expectationList = expectationValue.split('|').map(item => item.trim()).filter(item => item);
        expectationList.splice(index, 1);
        if (expectationList.length > 0) {
          attributes[foundKey] = expectationList.join('|');
        } else {
          delete attributes[foundKey];
        }
      }
    }

    // 更新人员属性
    const updateResponse = await updatePerson(selectedPersonId.value, {
      user_id: currentMisId.value,
      canonical_name: personDetail.canonical_name,
      aliases: personDetail.aliases || '',
      relationships: Array.isArray(personDetail.relationships) ? personDetail.relationships : [],
      profile_summary: personDetail.profile_summary || '',
      key_attributes: attributes,
      avatar: personDetail.avatar || '',
      is_user: personDetail.is_user || false,
    });

    console.log('📡 [relationGraph.vue] 删除生活方式项目响应:', updateResponse);

    if (updateResponse && updateResponse.result === 'success') {
      console.log('✅ [relationGraph.vue] 生活方式项目删除成功');
      showSuccessToast('删除成功');

      // 关闭删除对话框
      closeDeleteDialog();

      // 刷新关系图数据以更新PersonDetailPopup
      await loadRelationData();
    } else {
      console.warn('⚠️ [relationGraph.vue] 删除生活方式项目失败:', updateResponse);
      showFailToast('删除失败');
    }
  } catch (error) {
    console.error('❌ [relationGraph.vue] 删除生活方式项目失败:', error);
    showFailToast('删除失败');
  }
};

// 处理添加提醒弹窗关闭
const handleCloseAddReminderDialog = () => {
  showAddReminderDialog.value = false;
  reminderToEdit.value = null; // 清空编辑状态

  // 清除EChart节点的hover状态，解决手机端hover状态不消失的问题
  if (echartRelationGraphRef.value && echartRelationGraphRef.value.clearHoverState) {
    echartRelationGraphRef.value.clearHoverState();
  }
};

// 处理编辑提醒弹窗关闭
const handleCloseEditReminderDialog = () => {
  showEditReminderDialog.value = false;
  reminderToEditWithNaturalLanguage.value = null; // 清空编辑状态

  // 清除EChart节点的hover状态，解决手机端hover状态不消失的问题
  if (echartRelationGraphRef.value && echartRelationGraphRef.value.clearHoverState) {
    echartRelationGraphRef.value.clearHoverState();
  }
};

// 处理编辑提醒成功
const handleReminderEditSuccess = () => {
  // 刷新提醒列表
  void loadReminders(currentMisId.value);
  showSuccessToast('提醒编辑成功');
};

// 处理历史记录侧边栏关闭
const handleCloseHistorySidebar = () => {
  showHistorySidebar.value = false;

  // 清除EChart节点的hover状态，解决手机端hover状态不消失的问题
  if (echartRelationGraphRef.value && echartRelationGraphRef.value.clearHoverState) {
    echartRelationGraphRef.value.clearHoverState();
  }
};

// 处理备忘录侧边栏关闭
const handleCloseMemoSidebar = () => {
  showMemoSidebar.value = false;

  // 清除EChart节点的hover状态，解决手机端hover状态不消失的问题
  if (echartRelationGraphRef.value && echartRelationGraphRef.value.clearHoverState) {
    echartRelationGraphRef.value.clearHoverState();
  }
};

// 处理提醒添加/编辑成功
const handleReminderAddSuccess = () => {
  // 关闭弹窗
  showAddReminderDialog.value = false;
  reminderToEdit.value = null; // 清空编辑状态

  // 延迟1100ms后重新加载提醒数据，确保ES数据库已完成写入
  setTimeout(async () => {
    console.log('🔄 [relationGraph.vue] 延迟刷新提醒列表（添加/编辑提醒成功）');
    try {
      await loadReminders(currentMisId.value);
      console.log('✅ [relationGraph.vue] 提醒操作成功，已刷新提醒列表');
    } catch (error) {
      console.error('❌ [relationGraph.vue] 刷新提醒列表失败:', error);
    }
  }, 1100);
};

// 处理核心节点按钮点击事件
const handleCoreNodeClick = () => {
  console.log('🔄 [relationGraph.vue] 核心节点按钮点击，切换到人员列表视图');
  // 切换到人员列表视图
  activeGraphTab.value = 'list';
};

// 处理节点点击事件
const handleNodeClick = (nodeData: { id: string; person_id?: string }) => {
  console.log('🔄 [relationGraph.vue] 节点点击事件:', nodeData);
  console.log('🔄 [relationGraph.vue] 当前用户ID:', currentMisId.value);
  console.log('🔄 [relationGraph.vue] 节点详细信息:', {
    nodeId: nodeData.id,
    personId: nodeData.person_id,
    hasPersonId: !!nodeData.person_id,
    personIdType: typeof nodeData.person_id,
  });

  // 检查是否有person_id
  if (!nodeData.person_id) {
    console.warn('⚠️ [relationGraph.vue] 节点缺少person_id信息');
    console.warn('⚠️ [relationGraph.vue] 可用的节点数据:', echartGraphData.value?.nodes);
    return;
  }

  // 处理核心节点的点击 - 显示用户详情弹窗
  if (nodeData.id === currentMisId.value) {
    console.log('✅ [relationGraph.vue] 点击核心节点，显示用户详情弹窗');
    // 使用从API返回的真实person_id，而不是硬编码的'user_profile'
    selectedPersonId.value = nodeData.person_id;
    selectedPersonName.value = nodeData.id; // 使用用户ID作为显示名称
    selectedIsUserProfile.value = true; // 标记为用户档案
    showPersonDetailPopup.value = true;
    console.log('✅ [relationGraph.vue] 核心节点使用真实person_id:', nodeData.person_id);
    return;
  }

  // 处理非核心节点的点击 - 显示人员详情弹窗
  // 设置弹窗数据并显示
  selectedPersonId.value = nodeData.person_id;
  selectedPersonName.value = nodeData.id; // 使用canonical_name作为显示名称
  selectedIsUserProfile.value = false; // 标记为非用户档案
  showPersonDetailPopup.value = true;

  console.log('✅ [relationGraph.vue] 显示人员详情弹窗:', {
    currentMisId: currentMisId.value,
    personId: selectedPersonId.value,
    personName: selectedPersonName.value,
    personIdType: typeof selectedPersonId.value,
    personIdLength: selectedPersonId.value?.length,
  });
};

// EChart关系图组件引用
const echartRelationGraphRef = ref<{
  clearHoverState: () => void;
  highlightNodes: (personIds: string[]) => void;
  resize: () => void;
} | null>(null);

// 关闭人员详情弹窗
const closePersonDetailPopup = () => {
  showPersonDetailPopup.value = false;
  selectedPersonId.value = '';
  selectedPersonName.value = '';
  selectedIsUserProfile.value = false;

  // 清除EChart节点的hover状态，解决手机端hover状态不消失的问题
  if (echartRelationGraphRef.value && echartRelationGraphRef.value.clearHoverState) {
    echartRelationGraphRef.value.clearHoverState();
  }

  // 弹窗关闭后，延迟重新调整EChart图表尺寸
  void nextTick(() => {
    setTimeout(() => {
      if (echartRelationGraphRef.value && echartRelationGraphRef.value.resize) {
        console.log('🔄 [relationGraph.vue] 弹窗关闭后重新调整EChart尺寸');
        echartRelationGraphRef.value.resize();
      }
    }, 100);
  });
};

// 处理人员列表中的人员详情显示
const handlePersonListShowDetail = (personId: string, personName: string) => {
  selectedPersonId.value = personId;
  selectedPersonName.value = personName;
  selectedIsUserProfile.value = false; // 人员列表中的都不是用户档案
  showPersonDetailPopup.value = true;
};

// 处理人员列表中的编辑人员
const handlePersonListEditPerson = (person: IPersonData) => {
  personToEdit.value = person;
  showPersonEditDialog.value = true;
};

// 关闭编辑人员弹窗
const closePersonEditDialog = () => {
  showPersonEditDialog.value = false;
  personToEdit.value = null;

  // 弹窗关闭后，延迟重新调整EChart图表尺寸
  void nextTick(() => {
    setTimeout(() => {
      if (echartRelationGraphRef.value && echartRelationGraphRef.value.resize) {
        console.log('🔄 [relationGraph.vue] 编辑人员弹窗关闭后重新调整EChart尺寸');
        echartRelationGraphRef.value.resize();
      }
    }, 100);
  });
};

// 处理编辑人员成功
const handlePersonEditSuccess = async () => {
  // 刷新关系图
  await handleRefreshRelationGraph();
  // 关闭编辑弹窗
  closePersonEditDialog();
};

// PersonDetailPopup内部二级弹窗事件处理函数
const handleShowPersonDetailAddEventDialog = (personId: string) => {
  personDetailPersonId.value = personId;
  showPersonDetailAddEventDialog.value = true;
};

const handleShowPersonDetailEditEventDialog = (eventData: IEvent) => {
  personDetailEventData.value = eventData;
  showPersonDetailEditEventDialog.value = true;
};

const handleShowPersonDetailAddReminderDialog = (personId: string) => {
  personDetailPersonId.value = personId;
  showPersonDetailAddReminderDialog.value = true;
};

const handleShowPersonDetailEditReminderDialog = (reminderData: IReminder) => {
  personDetailReminderData.value = reminderData;
  showPersonDetailEditReminderDialog.value = true;
};

const handleShowPersonDetailNaturalEditReminderDialog = (reminderData: IReminder) => {
  personDetailReminderData.value = reminderData;
  showPersonDetailNaturalEditReminderDialog.value = true;
};

// PersonDetailPopup内部二级弹窗成功处理函数
const handlePersonDetailAddEventSuccess = () => {
  showPersonDetailAddEventDialog.value = false;

  // 刷新PersonDetailPopup中的MemorySection数据
  setTimeout(() => {
    if (personDetailPopupRef.value) {
      personDetailPopupRef.value.refreshMemoryData();
    }
  }, 1100);

  // 不需要刷新关系图，因为添加事件不会影响关系图的显示
};

const handlePersonDetailEditEventSuccess = (updatedEvent: IEvent) => {
  console.log('✅ [relationGraph.vue] 事件编辑成功:', updatedEvent);
  showPersonDetailEditEventDialog.value = false;
  personDetailEventData.value = null;

  // 刷新PersonDetailPopup中的MemorySection数据
  setTimeout(() => {
    if (personDetailPopupRef.value) {
      personDetailPopupRef.value.refreshMemoryData();
    }
  }, 1100);

  // 不需要刷新关系图，因为编辑事件不会影响关系图的显示
};

const handlePersonDetailAddReminderSuccess = () => {
  showPersonDetailAddReminderDialog.value = false;
  // 刷新关系图
  void handleRefreshRelationGraph();
};

const handlePersonDetailNaturalEditReminderSuccess = () => {
  showPersonDetailNaturalEditReminderDialog.value = false;
  personDetailReminderData.value = null;
  // 刷新关系图
  void handleRefreshRelationGraph();
};

// 刷新关系图数据
const handleRefreshRelationGraph = async () => {
  try {
    console.log('🔄 [relationGraph.vue] 刷新关系图数据...');

    // 设置加载状态，避免UI闪烁
    loading.value = true;

    // 重新获取关系图数据
    const relationData = await getRelationGraphData(currentMisId.value);
    console.log('📡 [relationGraph.vue] 刷新后的关系图数据:', relationData);

    graphData.value = relationData;

    // 重新获取原始的persons数据
    const personsResponse = await getPersons({
      userId: currentMisId.value,
      limit: 100,
      offset: 0,
    });

    if (personsResponse.result === 'success' && personsResponse.persons) {
      personsData.value = personsResponse.persons;
      console.log('✅ [relationGraph.vue] 刷新后的persons数据:', personsData.value);
    }

    // 重新获取用户档案信息（包含核心用户的头像）
    let userProfile: IPersonData | undefined;
    try {
      const userProfileResponse = await getUserProfile({
        user_id: currentMisId.value,
      });
      if (userProfileResponse.result === 'success' && userProfileResponse.person) {
        userProfile = userProfileResponse.person;
        console.log('✅ [relationGraph.vue] 刷新后的用户档案:', userProfile);
      }
    } catch (error) {
      console.warn('⚠️ [relationGraph.vue] 刷新时获取用户档案失败:', error);
    }

    // 转换为Echart关系图数据格式
    echartGraphData.value = transformToEchartData(
      relationData,
      currentMisId.value,
      personsData.value,
      userProfile,
    );
    console.log(
      '✅ [relationGraph.vue] 关系图数据刷新成功，新的节点数量:',
      echartGraphData.value?.nodes?.length || 0,
    );

    // 如果当前在人员列表视图，也刷新人员列表数据
    if (activeGraphTab.value === 'list' && personListRef.value) {
      console.log('🔄 [relationGraph.vue] 同时刷新人员列表数据...');
      await personListRef.value.loadPersons();
      console.log('✅ [relationGraph.vue] 人员列表数据刷新完成');
    }
  } catch (error) {
    console.error('❌ [relationGraph.vue] 刷新关系图数据失败:', error);
    showFailToast('刷新关系图数据失败');
  } finally {
    // 确保加载状态被重置
    loading.value = false;
  }
};

// 响应式调整图形尺寸
const updateGraphSize = () => {
  const container = document.querySelector('.graph-container');
  if (container) {
    graphWidth.value = container.clientWidth;
    graphHeight.value = container.clientHeight;
  }
};

// 从index.vue迁移过来的函数
// 处理添加新对话按钮点击
const handleAddChat = async () => {
  // 在关系图页面点击添加新对话按钮，跳转到chat.vue页面
  await router.push({
    name: 'chat-conversation',
    params: { title: 'new' }, // 使用固定的'new'参数，chat.vue会生成实际的conversationId
  });
};

// 切换历史侧边栏显示状态
const toggleHistorySidebar = () => {
  showHistorySidebar.value = !showHistorySidebar.value;
};

// 处理备忘录按钮点击
const handleMemo = () => {
  console.log('🔄 [relationGraph.vue] 备忘录按钮点击，显示备忘录侧边栏');
  showMemoSidebar.value = true;
};

// Tab切换方法
const toggleGraphView = () => {
  const newTab = activeGraphTab.value === 'graph' ? 'list' : 'graph';
  activeGraphTab.value = newTab;
  console.log('🔄 [relationGraph.vue] 切换图表视图:', newTab);
};

// 处理语音播放切换
const handleChatPlay = () => {
  isChatPlay.value = !isChatPlay.value;
  console.log('语音播放状态切换:', isChatPlay.value);
};

// 处理首页按钮点击
const handleHome = async () => {
  console.log('🏠 [relationGraph.vue] 点击home按钮，跳转到index页面聊天界面');
  // 清理sessionStorage数据
  sessionStorage.removeItem('returnToPersonDetail');
  sessionStorage.removeItem('relationGraphSource');
  sessionStorage.removeItem('chatFeature'); // 清理功能标记
  // 设置从home按钮跳转的标记，让index页面直接显示聊天界面
  sessionStorage.setItem('fromHomeButton', 'true');
  await router.push({
    name: 'chat', // 跳转到index页面
  });
};

// 处理回到首页按钮点击
const handleBackToIndex = async () => {
  console.log('🏠 [relationGraph.vue] 点击回到首页按钮，跳转到index页面');
  // 清理sessionStorage数据
  sessionStorage.removeItem('returnToPersonDetail');
  sessionStorage.removeItem('relationGraphSource');
  sessionStorage.removeItem('chatFeature'); // 清理功能标记
  await router.push({
    name: 'chat', // 跳转到index页面
  });
};

// 处理关系拓扑按钮点击（在关系图页面中，这个函数可能不需要，但为了保持一致性保留）
const handleRelationship = () => {
  console.log('已在关系拓扑页面');
  // 在关系图页面点击关系拓扑按钮，可以不做任何操作或者刷新数据
};

// 处理会话选择
const handleSelectConversation = (data: unknown) => {
  // 在关系图页面选择历史会话时，跳转到聊天页面并传递会话数据
  showHistorySidebar.value = false;

  // 检查数据格式
  if (data && typeof data === 'object' && 'conversationId' in data && 'title' in data) {
    const conversationData = data as { conversationId: string; title: string };

    // 将会话数据临时存储到sessionStorage，避免URL参数过长
    sessionStorage.setItem('pendingConversationRestore', JSON.stringify(data));

    // 跳转到聊天页面，使用会话标题作为路由参数
    void router.push({
      name: 'chat-conversation',
      params: { title: conversationData.title },
    });
  } else {
    console.error('❌ [relationGraph.vue] 会话数据格式错误:', data);
  }
};

// 用户头像相关函数
const getRandomColor = (misId: string) => {
  // 简单的颜色生成逻辑
  const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'];
  const index = misId.length % colors.length;
  return colors[index];
};

const getAvatarLetter = (misId: string) => {
  // 返回用户ID的第一个字符作为头像字母
  return misId ? misId.charAt(0).toUpperCase() : 'U';
};

// 处理开始聊天按钮点击
const handleStartChat = async () => {
  console.log('🔄 [relationGraph.vue] 开始聊天按钮点击，跳转到聊天页面');
  await router.push({
    name: 'chat-conversation',
    params: { title: 'new' },
  });
};

// 搜索相关方法
const handleSearch = async (query: string) => {
  console.log('🔄 [relationGraph.vue] 搜索人员:', query);

  if (!query.trim()) {
    showFailToast('请输入搜索内容');
    return;
  }

  isSearching.value = true;

  try {
    console.log('🔄 [relationGraph.vue] 开始搜索人员...', {
      userId: currentMisId.value,
      name: query.trim(),
    });

    const response = await searchPerson({
      user_id: currentMisId.value,
      name: query.trim(),
      limit: 10,
    });

    console.log('📡 [relationGraph.vue] 搜索人员响应:', response);

    if (response && response.result === 'success') {
      if (response.persons && response.persons.length > 0) {
        // 过滤掉用户自己
        const foundPersons = response.persons.filter((person) => !person.is_user);

        if (foundPersons.length > 0) {
          console.log('✅ [relationGraph.vue] 搜索成功，找到', foundPersons.length, '个人员');

          if (activeGraphTab.value === 'graph') {
            // 在关系图视图中高亮找到的节点
            highlightNodesInGraph(foundPersons);
          } else if (activeGraphTab.value === 'list') {
            // 在人员列表视图中显示搜索结果
            personListSearchResults.value = foundPersons;
            isPersonListSearchMode.value = true;
          }
        } else {
          console.log('⚠️ [relationGraph.vue] 搜索成功但无结果');
          showSuccessToast('未找到相关人员');

          if (activeGraphTab.value === 'list') {
            // 在人员列表视图中显示空结果
            personListSearchResults.value = [];
            isPersonListSearchMode.value = true;
          }
        }
      } else {
        console.log('⚠️ [relationGraph.vue] 搜索成功但无结果');
        showSuccessToast('未找到相关人员');

        if (activeGraphTab.value === 'list') {
          // 在人员列表视图中显示空结果
          personListSearchResults.value = [];
          isPersonListSearchMode.value = true;
        }
      }
    } else {
      console.warn('⚠️ [relationGraph.vue] 搜索失败:', response);
      showFailToast('搜索失败');
    }
  } catch (error) {
    console.error('❌ [relationGraph.vue] 搜索人员失败:', error);
    showFailToast('搜索失败');
  } finally {
    isSearching.value = false;
  }
};

const handleAddPerson = () => {
  console.log('🔄 [relationGraph.vue] 添加人员按钮点击');
  showAddPersonDialog.value = true;
};

const handleSearchInput = (query: string) => {
  console.log('🔄 [relationGraph.vue] 搜索输入变化:', query);

  // 如果搜索框被清空
  if (!query.trim()) {
    // 如果当前在人员列表视图的搜索模式，则退出搜索模式
    if (activeGraphTab.value === 'list' && isPersonListSearchMode.value) {
      isPersonListSearchMode.value = false;
      personListSearchResults.value = [];
      console.log('🔄 [relationGraph.vue] 搜索框清空，退出人员列表搜索模式');
    }

    // 如果当前在关系图视图，清除高亮状态
    if (activeGraphTab.value === 'graph' && echartRelationGraphRef.value) {
      echartRelationGraphRef.value.clearHoverState();
      console.log('🔄 [relationGraph.vue] 搜索框清空，清除关系图高亮状态');
    }
  }
};

// 在关系图中高亮节点
const highlightNodesInGraph = (foundPersons: IPersonData[]) => {
  if (!echartRelationGraphRef.value) {
    console.warn('⚠️ [relationGraph.vue] EChart关系图组件引用不存在');
    return;
  }

  // 获取找到的人员ID列表
  const foundPersonIds = foundPersons.map((person) => person.person_id);
  console.log('🔄 [relationGraph.vue] 准备高亮节点:', foundPersonIds);

  // 调用EChart组件的高亮方法
  console.log(
    '✨ [relationGraph.vue] 高亮节点:',
    foundPersons.map((p) => p.canonical_name),
  );

  // 调用EChart组件的高亮功能
  echartRelationGraphRef.value.highlightNodes(foundPersonIds);
};

// 关闭添加人员对话框
const closeAddPersonDialog = () => {
  showAddPersonDialog.value = false;

  // 弹窗关闭后，延迟重新调整EChart图表尺寸
  void nextTick(() => {
    setTimeout(() => {
      if (echartRelationGraphRef.value && echartRelationGraphRef.value.resize) {
        console.log('🔄 [relationGraph.vue] 添加人员弹窗关闭后重新调整EChart尺寸');
        echartRelationGraphRef.value.resize();
      }
    }, 100);
  });
};

// 处理添加人员成功
const handleAddPersonSuccess = async () => {
  // 刷新关系图数据（包括人员列表）
  await handleRefreshRelationGraph();
  // 关闭对话框
  closeAddPersonDialog();
};

const handleAvatarClick = () => {
  console.log('用户头像点击');
  // 可以在这里添加用户头像点击的逻辑，比如显示用户信息弹窗
};

// 处理人员头像点击 - 新增方法，用于从PersonDetailPopup触发
const handlePersonAvatarClick = (personData: IPersonData) => {
  console.log('人员头像点击，显示头像选择弹窗:', personData);
  currentPersonForAvatar.value = personData;
  showAvatarSelectionDialog.value = true;
};

// 处理头像选择
const handleAvatarSelect = async (selectedAvatarId: string) => {
  console.log('✅ [relationGraph.vue] 选择头像:', selectedAvatarId);

  if (!currentPersonForAvatar.value) return;

  try {
    // 构建更新数据，保持与PersonDetailPopup相同的逻辑
    const personData = currentPersonForAvatar.value;

    // 处理aliases字段
    const submitAliases = personData.aliases || '';

    const response = await updatePerson(personData.person_id, {
      user_id: currentMisId.value,
      canonical_name: personData.canonical_name,
      aliases: submitAliases,
      relationships: personData.relationships as string[],
      profile_summary: personData.profile_summary,
      key_attributes: personData.key_attributes as Record<string, unknown>,
      is_user: personData.is_user,
      avatar: selectedAvatarId,
    });

    if (response && response.result === 'success') {
      console.log('✅ [relationGraph.vue] 头像更新成功');
      showSuccessToast('头像更新成功');

      // 如果更新的是用户自己的头像，同时更新userStore
      if (personData.is_user) {
        userStore.updateUserAvatar(selectedAvatarId);
        console.log('✅ [relationGraph.vue] 用户头像已同步到userStore');
      }

      // 刷新关系图数据
      await handleRefreshRelationGraph();

      // 如果PersonDetailPopup是打开的且显示的是当前更新头像的人物，刷新它的数据
      if (showPersonDetailPopup.value && personDetailPopupRef.value &&
          selectedPersonId.value === personData.person_id) {
        console.log('🔄 [relationGraph.vue] 刷新PersonDetailPopup数据 - 当前显示的人物头像已更新');
        await personDetailPopupRef.value.refreshPersonDetail();
      }
    } else {
      console.warn('⚠️ [relationGraph.vue] 头像更新失败:', response);
      showFailToast('头像更新失败');
    }
  } catch (error) {
    console.error('❌ [relationGraph.vue] 头像更新失败:', error);
    showFailToast('头像更新失败');
  } finally {
    showAvatarSelectionDialog.value = false;
    currentPersonForAvatar.value = null;
  }
};

// 处理上传头像
const handleUploadAvatar = () => {
  showAvatarSelectionDialog.value = false;
  // 触发隐藏的AvatarUpload组件的上传功能
  if (avatarUploadRef.value && avatarUploadRef.value.triggerUpload) {
    avatarUploadRef.value.triggerUpload();
  }
};

// 处理头像上传成功
const handleAvatarUploadSuccess = async (url: string) => {
  console.log('✅ [relationGraph.vue] 头像上传成功:', url);
  await handleAvatarSelect(url);
};

// 处理头像上传失败
const handleAvatarUploadError = (error: string) => {
  console.error('❌ [relationGraph.vue] 头像上传失败:', error);
};

// 发送聊天消息的方法
const sendChatMessage = async (messageContent: string) => {
  if (!messageContent.trim() || !currentMisId.value) {
    return;
  }

  console.log('🚀 [relationGraph.vue] 开始发送聊天消息:', messageContent);

  // 如果有正在进行的请求，先取消它
  if (streamController.value) {
    console.log('🔄 [relationGraph.vue] 取消正在进行的请求');
    streamController.value.abort();
    streamController.value = null;
  }

  // 重置状态
  isStoppedByUser.value = false;
  isTypewriterStarted.value = false;

  // 设置加载状态
  chatStore.setAnswerStatus(AnswerStatusEnum.LOADING);

  // 添加助手消息占位符
  const assistantMessage: IChatStreamContent = {
    role: 'assistant',
    content: '',
    key: Date.now() + 1,
    isFinish: false,
    reasoningData: {} as IReasoningData,
    isToolCallLoading: false,
  };
  chatMessages.value.push(assistantMessage);

  try {
    // 构建请求数据
    const requestData: IChatRequest = {
      content: messageContent,
      conversation_id: conversationId.value,
      user_id: currentMisId.value,
    };

    // 创建新的 AbortController
    streamController.value = new AbortController();

    // 开始流式聊天
    await streamChat(
      requestData,
      {
        onMessage: (content: string, isFinal: boolean) => {
          if (isStoppedByUser.value) {
            console.log('🚫 [relationGraph.vue] 用户已停止，忽略消息片段');
            return;
          }

          // 添加消息到打字机队列
          typewriter.add(content);

          // 启动打字机（如果还未启动）
          if (!isTypewriterStarted.value) {
            console.log('🚀 [relationGraph.vue] 启动typewriter，开始显示内容');
            isTypewriterStarted.value = true;
            typewriter.start();
          }

          if (isFinal) {
            console.log('✅ [relationGraph.vue] 收到最终消息，标记完成');
            typewriter.markFinished();
            const lastMessage = chatMessages.value[chatMessages.value.length - 1];
            if (lastMessage && lastMessage.role === 'assistant') {
              lastMessage.isFinish = true;
            }
          }
        },
        onPreResponse: (responseContent: string, stage: string) => {
          console.log('📝 [relationGraph.vue] 预响应:', {
            content: responseContent,
            stage,
          });
        },
        onToolCall: (toolCall: IToolCall) => {
          console.log('🔧 [relationGraph.vue] 工具调用:', toolCall);
        },
        onRecommendations: (recommendations: string[]) => {
          console.log('💡 [relationGraph.vue] 推荐问题:', recommendations);
        },
        onEnd: () => {
          console.log('✅ [relationGraph.vue] 聊天流结束');
          chatStore.setAnswerStatus(AnswerStatusEnum.SUCCESS);
        },
        onError: (error: Error) => {
          console.error('❌ [relationGraph.vue] 聊天错误:', error);
          handleChatError('聊天过程中发生错误，请稍后重试');
        },
        onClose: () => {
          console.log('🔒 [relationGraph.vue] 聊天连接关闭');
          // 连接关闭时重置 streamController，但保持会话数据
          streamController.value = null;
        },
      },
      streamController.value.signal,
    );
  } catch (error) {
    console.error('❌ [relationGraph.vue] 发送聊天消息失败:', error);
    handleChatError('发送消息失败，请稍后重试');
  }
};

// 处理聊天错误
const handleChatError = (errorText: string) => {
  console.log('❌ [relationGraph.vue] 处理聊天错误:', errorText);

  const lastMessage = chatMessages.value[chatMessages.value.length - 1];
  if (lastMessage && lastMessage.role === 'assistant' && !lastMessage.isFinish) {
    lastMessage.content = errorText;
    lastMessage.isFinish = true;
    lastMessage.key = Date.now();
  } else {
    chatMessages.value.push({
      role: 'assistant',
      content: errorText,
      key: Date.now(),
      isFinish: true,
      reasoningData: {} as IReasoningData,
    });
  }

  // 结束打字机
  typewriter.markFinished();
  setTimeout(() => {
    typewriter.done();
  }, 100);

  // 重置状态，但保持会话数据
  setTimeout(() => {
    chatStore.setAnswerStatus(AnswerStatusEnum.SUCCESS);
    isTypewriterStarted.value = false;
    // 不清除 streamController，让它在下次发送时重新创建
  }, 1000);
};

// 对话相关方法
// 处理输入发送
const handleInputSend = async (message: string) => {
  console.log('🔄 [relationGraph.vue] 收到输入消息:', message);

  if (!message.trim()) {
    return;
  }

  // 只在没有会话ID时创建新的会话（保持会话连续性）
  if (!conversationId.value) {
    try {
      if (currentMisId.value && currentMisId.value !== 'unknown_user') {
        console.log('🔄 [relationGraph.vue] 创建新会话...');
        const response = await createConversation({
          user_id: currentMisId.value,
        });

        if (response.success && response.conversation_id) {
          conversationId.value = response.conversation_id;
          console.log('✅ [relationGraph.vue] 创建新会话成功，会话ID:', conversationId.value);
        } else {
          console.warn('⚠️ [relationGraph.vue] 创建会话失败，使用本地会话ID');
          conversationId.value = `conv_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        }
      } else {
        console.warn('⚠️ [relationGraph.vue] 用户ID无效，使用本地会话ID');
        conversationId.value = `conv_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      }
    } catch (error) {
      console.error('❌ [relationGraph.vue] 创建会话失败:', error);
      conversationId.value = `conv_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    }
  } else {
    console.log('🔄 [relationGraph.vue] 使用现有会话ID:', conversationId.value);
  }

  // 显示对话框
  showChatDialog.value = true;

  // 添加用户消息到聊天列表
  chatMessages.value.push({
    role: 'user',
    content: message,
    key: Date.now(),
    isFinish: true,
    reasoningData: {} as IReasoningData,
  });

  // 调用聊天API
  void sendChatMessage(message);
};

// 处理停止生成
const handleStop = () => {
  console.log('🛑 [relationGraph.vue] 停止生成');

  // 设置停止标志
  isStoppedByUser.value = true;

  // 取消流式请求
  if (streamController.value) {
    streamController.value.abort();
    streamController.value = null;
  }

  // 停止打字机
  typewriter.stop();
  isTypewriterStarted.value = false;

  // 标记最后一条消息为完成
  if (chatMessages.value.length > 0) {
    const lastMessage = chatMessages.value[chatMessages.value.length - 1];
    if (lastMessage && lastMessage.role === 'assistant' && !lastMessage.isFinish) {
      lastMessage.isFinish = true;
      lastMessage.content = lastMessage.content || '回答被中断';
    }
  }

  // 重置状态
  chatStore.setAnswerStatus(AnswerStatusEnum.SUCCESS);
};

// 处理录音状态变化
const handleRecordingStatus = (recording: boolean) => {
  isRecording.value = recording;
  console.log('🎤 [relationGraph.vue] 录音状态变化:', recording);
};

// 处理关闭对话框
const handleCloseChatDialog = () => {
  showChatDialog.value = false;
  console.log('❌ [relationGraph.vue] 关闭对话框，保留会话数据以支持连续对话');

  // 不清除会话数据，保持对话连续性
  // chatMessages.value = [];
  // conversationId.value = '';

  // 停止正在进行的聊天
  if (streamController.value) {
    streamController.value.abort();
    streamController.value = null;
  }

  // 停止打字机
  typewriter.stop();
  isTypewriterStarted.value = false;

  // 重置聊天状态
  chatStore.setAnswerStatus(AnswerStatusEnum.SUCCESS);
  isStoppedByUser.value = false;
};

// 处理对话框发送消息
const handleChatDialogSend = (message: string) => {
  void handleInputSend(message);
};

// 清除会话数据（用于开始新对话）
const clearChatSession = async () => {
  console.log('🧹 [relationGraph.vue] 清除会话数据，开始新对话');

  // 停止正在进行的聊天
  if (streamController.value) {
    streamController.value.abort();
    streamController.value = null;
  }

  // 停止打字机
  typewriter.stop();
  isTypewriterStarted.value = false;

  // 清除会话数据
  chatMessages.value = [];
  conversationId.value = '';

  // 重置聊天状态
  chatStore.setAnswerStatus(AnswerStatusEnum.SUCCESS);
  isStoppedByUser.value = false;

  // 预创建新会话（可选，也可以等到下次发送消息时再创建）
  try {
    if (currentMisId.value && currentMisId.value !== 'unknown_user') {
      console.log('🔄 [relationGraph.vue] 预创建新会话...');
      const response = await createConversation({
        user_id: currentMisId.value,
      });

      if (response.success && response.conversation_id) {
        conversationId.value = response.conversation_id;
        console.log('✅ [relationGraph.vue] 预创建新会话成功，会话ID:', conversationId.value);
      }
    }
  } catch (error) {
    console.error('❌ [relationGraph.vue] 预创建会话失败:', error);
    // 失败时不设置会话ID，等到下次发送消息时再创建
  }
};

// 处理开始体验
const handleGetStarted = () => {
  console.log('🚀 [relationGraph.vue] 开始体验');
  // 显示对话框
  showChatDialog.value = true;
};

// 处理重新生成
const handleRegenerate = (messageData: IChatStreamContent) => {
  console.log('🔄 [relationGraph.vue] 重新生成消息:', messageData);
  // TODO: 实现重新生成逻辑
};

// 处理表单提交
const handleFormSubmit = () => {
  // 表单提交时不做任何操作，因为实际的发送逻辑由inputBar组件的send事件处理
  console.log('表单提交被阻止，使用组件事件处理');
};

onMounted(async () => {
  updateGraphSize();
  window.addEventListener('resize', updateGraphSize);

  // 先加载关系数据，确保currentMisId已设置
  await loadRelationData();

  // 在获取用户信息后初始化主题（设置用户ID以支持后端同步）
  themeStore.setUserId(currentMisId.value);
  await themeStore.initTheme();
});

onUnmounted(() => {
  window.removeEventListener('resize', updateGraphSize);
});
</script>

<style lang="scss" scoped>
// 统一配色方案
:root {
  --primary-color: #00bcd4;
  --primary-color-light: rgba(0, 188, 212, 0.1);
  --primary-color-medium: rgba(0, 188, 212, 0.2);
  --primary-color-strong: rgba(0, 188, 212, 0.3);

  --accent-color: #00ffff;
  --accent-color-light: rgba(0, 255, 255, 0.1);
  --accent-color-medium: rgba(0, 255, 255, 0.2);
  --accent-color-strong: rgba(0, 255, 255, 0.3);

  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.9);
  --text-tertiary: rgba(255, 255, 255, 0.7);
  --text-disabled: rgba(255, 255, 255, 0.5);

  --bg-glass: rgba(30, 58, 138, 0.15);
  --bg-glass-hover: rgba(30, 58, 138, 0.25);
  --border-glass: rgba(255, 255, 255, 0.2);
  --border-accent: rgba(0, 255, 255, 0.3);

  --shadow-soft: 0 8px 32px rgba(0, 0, 0, 0.2);
  --shadow-strong: 0 20px 60px rgba(0, 0, 0, 0.3);
  --shadow-accent: 0 0 20px rgba(0, 255, 255, 0.2);
}

// 容器样式
.relation-graph-container {
  // 统一定义输入区高度变量，便于页面级底部预留空间计算
  --footer-height: 160px;
  // 背景现在由主题系统控制
  height: 100vh;
  // 在iOS上适配安全区域，确保内容不会被状态栏和底部安全区域遮挡
  height: calc(100vh - env(safe-area-inset-top, 0px));
  padding-top: env(safe-area-inset-top, 0px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative; // 为星空粒子提供定位基准

  // iOS Safari特定优化
  -webkit-overflow-scrolling: touch;
  // 防止iOS上的橡皮筋效果
  overscroll-behavior: none;
}

.relation-graph-page {
  width: 100%;
  height: 100%;
  display: flex;
  // 预留底部空间，避免被绝对定位的输入框遮挡
  // 可通过 --footer-height 调整输入区高度（默认 88px），并叠加安全区
  padding-bottom: calc(var(--footer-height, 88px) + env(safe-area-inset-bottom, 0px));
  flex-direction: column;
  overflow: hidden;
  position: relative; // 为弹窗提供定位基准
  z-index: 10; // 确保内容在星空粒子之上

  .header {
    width: 100%;
    flex-shrink: 0; // 防止头部被压缩
  }
}

// 搜索区域样式
.search-area {
  width: 100%;
  padding: 0px 20px;
  flex-shrink: 0; // 防止搜索区域被压缩
  background: transparent;
}

// 标签切换区域样式
.tab-switch-area {
  width: 100%;
  padding: 15px 20px;
  flex-shrink: 0;
  background: transparent;
}

.tab-switch-container {
  position: relative;
  display: flex;
  justify-content: center;
  gap: 0;
  background: var(--bg-glass);
  border: 2px solid var(--primary-color);
  border-radius: 32px;
  padding: 8px;
  backdrop-filter: blur(20px);
  transition: all 0.3s ease;
  overflow: hidden;
  width: 98%;
  margin: 0 auto;
  height: 64px;
}

.tab-button {
  flex: 1;
  padding: 16px 24px;
  border: none;
  border-radius: 24px;
  background: transparent;
  color: var(--primary-color);
  font-size: 22px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;

  &.active {
    background: var(--primary-color);
    color: var(--on-primary-text);
    font-weight: 700;
    box-shadow:
      0 0 10px var(--primary-color-medium),
      inset 0 0 10px rgba(255, 255, 255, 0.1);
  }


}

.graph-container {
  flex: 1;
  width: 100%;
  min-height: 0; // 允许flex子项收缩
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  position: relative;
  margin-bottom: 20px; // 与底部输入区更紧凑的间距
  background: transparent;
}

// 内容包装器
.graph-content-wrapper {
  flex: 1;
  display: flex;
  width: 100%;
  height: 100%;
  min-height: 0;
}

// 图表视图
.graph-view {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  .graph-content {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

// 列表视图
.list-view {
  width: 100%;
  height: 100%;
  display: flex;
}

.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.loading-text,
.error-text {
  color: var(--text-primary);
  font-size: 16px;
  text-align: center;
  padding: 20px;
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

// 欢迎界面容器
.welcome-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 800px;
  height: 89%; // 固定高度
  z-index: 10;
}

.welcome-content {
  border-radius: 16px;
  padding: 40px;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  box-shadow:
    0 0 12px rgba(0, 255, 255, 0.4),
    // 增强青色光晕
    0 8px 32px rgba(0, 0, 0, 0.7),
    // 增强深色外阴影，更远更深
    0 4px 16px rgba(0, 0, 0, 0.4),
    // 添加中层阴影
    inset 0 2px 0 rgba(255, 255, 255, 0.15),
    // 增强内部高光
    inset 0 -1px 0 rgba(0, 0, 0, 0.2); // 添加底部内阴影增加深度
  transition: all 0.3s ease;
  color: var(--page-text-primary);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  text-align: center;
  height: 100%; // 填满容器
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.welcome-header {
  text-align: center;
  margin-bottom: 40px;
}

.welcome-icon {
  margin-bottom: 24px;

  .assistant-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid var(--accent-color-medium);
    box-shadow: var(--shadow-accent);
  }
}

// 标题样式现在使用全局设计系统中的 .cyber-title 和 .cyber-subtitle

.welcome-features {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 40px;
}

// 功能卡片样式现在使用全局设计系统中的类
.feature-text {
  flex: 1;
}

.welcome-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}

// 按钮样式现在使用全局设计系统中的 .cyber-btn-primary 和 .cyber-btn-secondary
.cyber-btn-primary,
.cyber-btn-secondary {
  flex: 1;
  max-width: 280px; // 增加最大宽度以允许按钮更宽

  .btn-icon {
    font-size: 26px; // 增加6px (20px -> 26px)
  }
}

.reminder-container {
  width: 100%;
  height: auto; // 自适应高度
  min-height: 100px; // 进一步增加高度，确保不被遮挡
  background: transparent;
  border-top: 1px solid var(--border-glass); // 顶部边框
  display: flex;
  align-items: center;
  padding: 0px 12px; // 进一步增加上下padding
  flex-shrink: 0; // 防止提醒容器被压缩
  transition: all 0.3s ease;
  z-index: 20; // 设置更高的z-index，确保在老董假装说话之上

  // 紧凑模式 - 空状态下缩小提醒区域
  &.compact {
    height: auto; // 去除固定高度
    min-height: 90px; // 进一步增加紧凑模式的高度
    border-top: 1px solid var(--text-disabled);
    opacity: 0.7;
  }
}

.reminder-swiper-container {
  width: 100%;
  height: auto; // 自适应高度
  display: flex;
  align-items: center;
  background: transparent;

  // iOS Safari滚动优化
  -webkit-overflow-scrolling: touch;
  // 防止iOS上的橡皮筋效果影响滑动体验
  overscroll-behavior-x: contain;
}

.reminder-swiper {
  width: 100%;
  height: auto; // 自适应高度
  padding: 6px 0; // 缩小上下留白
}

.reminder-slide {
  width: 220px !important;
  height: auto; // 卡片按内容自适应
  display: flex;
  justify-content: center;
  align-items: center;
}

// 空状态容器样式
.empty-reminders-container {
  width: 100%;
  height: auto; // 不占满可用高度
  min-height: 56px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: transparent;
}

.empty-reminders-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30px;
  text-align: center;
}

.empty-reminders-text {
  color: var(--text-tertiary);
  font-size: 16px;
  font-weight: 400;
  line-height: 1.4;
  letter-spacing: 0.3px;
  margin-bottom: 16px;
}

.empty-add-card {
  display: flex;
  justify-content: center;
  align-items: center;
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

// 底部输入框样式 - 在页面内部固定在底部
.footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1100; // 设置比弹窗更高的z-index，确保inputBar始终在最上层
  // 不需要额外的padding-bottom，因为inputBar组件内部已经处理了安全区域适配
  // inputBar组件使用了 calc(env(safe-area-inset-bottom) + 36px) 来处理底部安全区域

  .input-wrapper {
    width: 100%;
    padding: 0px;
    background: transparent;
  }
}

// 老董假装说话样式 - 作为输入框的下半部分
.laodong-fake-speaking {
  padding: 0px 20px;
  display: flex;
  justify-content: flex-start;
  // 透明背景，与inputBar保持一致
  background: transparent;
  border: 2px solid var(--border-accent); // 添加左右边框
  border-top: none; // 去掉上边框
  border-radius: 0 0 20px 20px; // 只有下方圆角，与输入框形成整体
  margin-top: -2px; // 与输入框无缝连接
  // 移除模糊效果

  .fake-speaking-container {
    display: flex;
    align-items: center; // 改为居中对齐，一行显示
    gap: 8px; // 减少间距，从12px改为8px
    width: 100%; // 占满宽度

    .laodong-avatar {
      width: 45px; // 缩小头像，从80px改为54px（约缩小1/3）
      height: 45px;
      border-radius: 50%;
      overflow: hidden;
      flex-shrink: 0;
      border: 2px solid var(--border-accent);
      box-shadow: var(--shadow-medium);

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .fake-speaking-content {
      display: flex;
      align-items: center;
      gap: 8px;
      flex: 1;

      .fake-speaking-text {
        color: #000000; /* 改为黑色 */
        font-size: 28px; // 继续增大字体
        font-weight:400;
        line-height: 1.2;
        white-space: nowrap; // 不换行
      }

      .fake-speaking-dots {
        display: flex;
        gap: 4px;
        align-items: center;

        .dot {
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background: #000000; /* 改为黑色 */
          animation: fakeSpeakingPulse 1.5s ease-in-out infinite;

          &:nth-child(1) {
            animation-delay: 0s;
          }

          &:nth-child(2) {
            animation-delay: 0.3s;
          }

          &:nth-child(3) {
            animation-delay: 0.6s;
          }
        }
      }
    }
  }
}

// 假装说话动画
@keyframes fakeSpeakingPulse {
  0%, 60%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  30% {
    opacity: 1;
    transform: scale(1.2);
  }
}

// 移动端适配
@media (max-width: 768px) {
  .laodong-fake-speaking {
    padding: 0px 16px; // 减少移动端上下padding，从10px改为4px

    .fake-speaking-container {
      gap: 6px; // 减少移动端间距，从10px改为6px

      .laodong-avatar {
        width: 42px; // 缩小移动端头像，从64px改为42px（约缩小1/3）
        height: 42px;
      }

      .fake-speaking-content {
        gap: 6px;

        .fake-speaking-text {
          font-size: 26px; // 移动端也继续增大字体
          color: #000000; /* 移动端也改为黑色 */
        }

        .fake-speaking-dots {
          gap: 3px;

          .dot {
            width: 5px;
            height: 5px;
          }
        }
      }
    }
  }
}
</style>
