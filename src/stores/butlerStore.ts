// <PERSON> (老董) 全局轻量状态：控制 Composer/Float 互斥显示与建议文案
// 简单实现，使用 Vue refs 与 localStorage 进行一次性提示记忆。

import { ref } from 'vue'

export type ButlerSuggestion = { id: string; text: string }

const STORAGE_KEY_NOTE_DISMISSED = 'butler_note_dismissed'

function loadNoteDismissed(): boolean {
  try {
    return localStorage.getItem(STORAGE_KEY_NOTE_DISMISSED) === '1'
  } catch {
    return false
  }
}
function saveNoteDismissed(v: boolean) {
  try {
    localStorage.setItem(STORAGE_KEY_NOTE_DISMISSED, v ? '1' : '0')
  } catch {
    // Ignore storage errors
  }
}

const composerVisible = ref(true)
const floatVisible = ref(false)
const suggestions = ref<ButlerSuggestion[]>([])
const tip = ref<string | undefined>(undefined)
const noteDismissed = ref<boolean>(loadNoteDismissed())

class ButlerStore {
  get composerVisible() { return composerVisible }

  get floatVisible() { return floatVisible }

  get suggestions() { return suggestions }

  get tip() { return tip }

  get noteDismissed() { return noteDismissed }

  showComposer() {
    composerVisible.value = true
    floatVisible.value = false
  }

  showFloat() {
    composerVisible.value = false
    floatVisible.value = true
  }

  hideAll() {
    composerVisible.value = false
    floatVisible.value = false
  }

  setSuggestions(list: ButlerSuggestion[]) {
    suggestions.value = list
  }

  setTip(text?: string) {
    tip.value = text
  }

  dismissNote() {
    noteDismissed.value = true
    saveNoteDismissed(true)
  }
}

export const butlerStore = new ButlerStore()
