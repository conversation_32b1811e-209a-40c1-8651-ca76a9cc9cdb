// 备忘录本地状态（对齐 iOS 功能：新增、查看列表、修改、删除；无语音）
// 简单持久化：localStorage，键名 'warm_memos'

import { ref } from 'vue'

export type SmartMemo = {
  id: string
  text: string
  createdAt: number
}

const STORAGE_KEY = 'warm_memos'

function load(): SmartMemo[] {
  try {
    const raw = localStorage.getItem(STORAGE_KEY)
    if (!raw) return []
    const arr = JSON.parse(raw) as SmartMemo[]
    if (!Array.isArray(arr)) return []
    return arr
  } catch {
    return []
  }
}

function save(list: SmartMemo[]) {
  localStorage.setItem(STORAGE_KEY, JSON.stringify(list))
}

function uuid(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {
    // eslint-disable-next-line no-bitwise
    const r = Math.floor(Math.random() * 16)
    // eslint-disable-next-line no-bitwise
    const v = c === 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}

const state = ref<SmartMemo[]>(load())

class MemoStore {
  get list(): SmartMemo[] {
    // 最新在前（iOS 展示为倒序）
    return [...state.value].sort((a, b) => b.createdAt - a.createdAt)
  }

  add(text: string) {
    const item: SmartMemo = { id: uuid(), text: text.trim(), createdAt: Date.now() }
    state.value = [...state.value, item]
    save(state.value)
    return item
  }

  update(id: string, text: string) {
    const next = state.value.map(m => (m.id === id ? { ...m, text: text.trim() } : m))
    state.value = next
    save(state.value)
  }

  remove(id: string) {
    state.value = state.value.filter(m => m.id !== id)
    save(state.value)
  }

  clear() {
    state.value = []
    save(state.value)
  }
}

export const memoStore = new MemoStore()

// 添加一些初始示例数据（如果本地存储为空）
if (memoStore.list.length === 0) {
  memoStore.add('我住在北京')
  memoStore.add('明天要去上海出差')
  memoStore.add('我对花粉过敏')
}
