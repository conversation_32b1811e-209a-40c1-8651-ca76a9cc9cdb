// WeatherAIInterpretationCoordinator.ts
// 管理“深解读”加载步骤与结果，严格对齐 iOS 状态命名与语义
// 说明：AI 执行部分支持以可注入的 executor 实现，避免绑定未知 API 签名

import { ref } from 'vue'
import type { WeatherService, ILocationWeatherData } from './WeatherService'
import { WeatherInterpretationProcessor, type IParsedInterpretation } from './WeatherInterpretationProcessor'

export type CityInterpretation = { id?: string | number; cityName: string; text: string }

export interface ILoadingState {
  steps: string[]
  isFinished: boolean
}

export type AIExecutor = (prompt: string) => Promise<string> | AsyncIterable<string>

export class WeatherAIInterpretationCoordinator {
  isLoading = ref(false)

  loadingState = ref<ILoadingState>({ steps: [], isFinished: false })

  weatherInterpretation = ref<string>('')

  cityInterpretations = ref<CityInterpretation[]>([])

  // —— 调试日志（前台底部显示，仅开发用途，不改变 iOS 功能）
  debugLogs = ref<string[]>([])

  private processor = new WeatherInterpretationProcessor()

  reset() {
    this.isLoading.value = false
    this.loadingState.value = { steps: [], isFinished: false }
    this.weatherInterpretation.value = ''
    this.cityInterpretations.value = []
    this.debugLogs.value = []
  }

  private pushStep(text: string) {
    this.loadingState.value = {
      ...this.loadingState.value,
      steps: [...this.loadingState.value.steps, text],
    }
    this.log(text)
  }

  private finish() {
    this.loadingState.value = { ...this.loadingState.value, isFinished: true }
    this.isLoading.value = false
  }

  private log(text: string) {
    const ts = new Date().toISOString()
    this.debugLogs.value = [...this.debugLogs.value, `[${ts}] ${text}`]
  }

  /**
   * 生成深解读
   * @param cities 需要解读的城市名（与 iOS 对齐）
   * @param weatherService 提供城市 -> 天气数据 bundles
   * @param memoTexts 备忘录文本数组
   * @param executor 可选 AI 执行器，接收 prompt，返回完整字符串或可迭代流
   * @param context 可选上下文，若提供则使用 iOS 模板完整上下文
   */
  async generateWeatherInterpretation(
    cities: string[],
    weatherService: WeatherService,
    memoTexts: string[],
    executor?: AIExecutor,
    context?: {
      currentDate?: string
      currentLocationInfo?: string
      locationAnalysis?: string
      weatherDataNL?: string
    }
  ) {
    this.reset()
    this.isLoading.value = true

    try {
      this.pushStep('正在收集天气数据…')
      const store = weatherService.getStore()
      const bundles = store

      this.pushStep('正在构建提示词…')
      const prompt = this.processor.buildPrompt(
        cities,
        bundles,
        memoTexts?.join('\n') || '',
        {
          currentDate: context?.currentDate,
          memoTexts,
          currentLocationInfo: context?.currentLocationInfo,
          locationAnalysis: context?.locationAnalysis,
          weatherDataNL: context?.weatherDataNL,
        }
      )
      this.log(`Prompt 构建完成，长度=${prompt.length}`)

      if (!executor) {
        // 未注入执行器：将提示词作为单条文本输出，便于开发调试（不新增 iOS 功能，仅开发辅助）
        this.pushStep('等待 AI 执行器…')
        this.weatherInterpretation.value = prompt
        this.finish()
        return
      }

      this.pushStep('正在向 AI 发起请求…')
      const startedAt = Date.now()
      let fullText = ''
      const result = executor(prompt)

      if (isAsyncIterable(result)) {
        this.log('执行器：流式 AsyncIterable')
        // eslint-disable-next-line no-restricted-syntax
        for await (const chunk of result) {
          if (typeof chunk === 'string') fullText += chunk
        }
      } else {
        this.log('执行器：Promise 非流式')
        fullText = await (result)
      }
      this.log(`AI 返回完成，耗时=${Date.now() - startedAt}ms，长度=${fullText.length}`)

      this.pushStep('正在解析 AI 输出…')
      const parsed: ParsedInterpretation = this.processor.parse(fullText, cities[0])
      this.weatherInterpretation.value = parsed.interpretationText || ''
      this.cityInterpretations.value = parsed.cityInterpretations || []
      this.finish()
    } catch (e) {
      this.pushStep('解析失败或网络异常')
      this.log(`错误：${(e as { message?: string })?.message || e}`)
      this.finish()
    }
  }
}

function isAsyncIterable<T = unknown>(obj: unknown): obj is AsyncIterable<T> {
  return obj && typeof obj[Symbol.asyncIterator] === 'function'
}
